import 'package:flutter/material.dart';

class QuickSearchWidget extends StatefulWidget {
  const QuickSearchWidget({super.key});

  @override
  State<QuickSearchWidget> createState() => _QuickSearchWidgetState();
}

class _QuickSearchWidgetState extends State<QuickSearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  List<SearchResult> _searchResults = [];
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.search,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                const Text(
                  'البحث السريع',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Sized<PERSON>ox(height: 12),
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث في القرآن الكريم...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchResults = [];
                            _isSearching = false;
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Theme.of(context).cardColor,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: _performSearch,
              textInputAction: TextInputAction.search,
            ),
            if (_isSearching) ...[
              const SizedBox(height: 12),
              const LinearProgressIndicator(),
            ],
            if (_searchResults.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                constraints: const BoxConstraints(maxHeight: 200),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _searchResults.length > 5 ? 5 : _searchResults.length,
                  itemBuilder: (context, index) {
                    final result = _searchResults[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        title: Text(
                          result.text,
                          style: const TextStyle(fontSize: 16),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        subtitle: Text(
                          '${result.surahName} - آية ${result.ayahNumber}',
                          style: TextStyle(
                            color: Theme.of(context).textTheme.bodySmall?.color,
                          ),
                        ),
                        leading: CircleAvatar(
                          backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                          child: Text(
                            '${result.surahNumber}',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        onTap: () => _navigateToAyah(result),
                        dense: true,
                      ),
                    );
                  },
                ),
              ),
              if (_searchResults.length > 5) ...[
                const SizedBox(height: 8),
                Center(
                  child: TextButton(
                    onPressed: () => _showAllResults(),
                    child: Text('عرض جميع النتائج (${_searchResults.length})'),
                  ),
                ),
              ],
            ] else if (_searchController.text.isNotEmpty && !_isSearching) ...[
              const SizedBox(height: 12),
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.search_off,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لم يتم العثور على نتائج',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _performSearch(String query) async {
    if (query.length < 2) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      // محاكاة البحث - يمكن استبدالها بالبحث الفعلي
      await Future.delayed(const Duration(milliseconds: 500));
      
      final results = await _mockSearch(query);
      
      if (mounted) {
        setState(() {
          _searchResults = results;
          _isSearching = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
        });
      }
    }
  }

  Future<List<SearchResult>> _mockSearch(String query) async {
    // محاكاة نتائج البحث - يجب استبدالها بالبحث الفعلي في قاعدة البيانات
    final mockResults = [
      SearchResult(
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        surahName: 'الفاتحة',
        surahNumber: 1,
        ayahNumber: 1,
      ),
      SearchResult(
        text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        surahName: 'الفاتحة',
        surahNumber: 1,
        ayahNumber: 2,
      ),
      SearchResult(
        text: 'الرَّحْمَٰنِ الرَّحِيمِ',
        surahName: 'الفاتحة',
        surahNumber: 1,
        ayahNumber: 3,
      ),
    ];

    // تصفية النتائج بناءً على النص المدخل
    return mockResults
        .where((result) => result.text.contains(query) || result.surahName.contains(query))
        .toList();
  }

  void _navigateToAyah(SearchResult result) {
    // التنقل إلى الآية المحددة
    Navigator.of(context).pushNamed(
      '/quran/surah',
      arguments: {
        'surahNumber': result.surahNumber,
        'ayahNumber': result.ayahNumber,
      },
    );
  }

  void _showAllResults() {
    // عرض جميع النتائج في صفحة منفصلة
    Navigator.of(context).pushNamed(
      '/search/results',
      arguments: {
        'query': _searchController.text,
        'results': _searchResults,
      },
    );
  }
}

class SearchResult {
  final String text;
  final String surahName;
  final int surahNumber;
  final int ayahNumber;

  SearchResult({
    required this.text,
    required this.surahName,
    required this.surahNumber,
    required this.ayahNumber,
  });
}

class QuickSearchBar extends StatelessWidget {
  final VoidCallback? onTap;
  final String hintText;

  const QuickSearchBar({
    super.key,
    this.onTap,
    this.hintText = 'ابحث في القرآن الكريم...',
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => _showSearchDialog(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.search,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                hintText,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          constraints: const BoxConstraints(
            maxHeight: 500,
            maxWidth: 400,
          ),
          child: const QuickSearchWidget(),
        ),
      ),
    );
  }
}