import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/hadith_provider.dart';

class HadithScreen extends StatelessWidget {
  const HadithScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF9C27B0), Color(0xFFBA68C8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.article_rounded,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'الأحاديث النبوية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF9C27B0), Color(0xFFBA68C8)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.search_rounded, color: Colors.white),
              onPressed: () async {
                // نافذة بحث عن الأحاديث
                final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
                await showSearch(
                  context: context,
                  delegate: HadithSearchDelegate(hadithProvider: hadithProvider),
                );
              },
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.favorite_rounded, color: Colors.white),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FavoriteHadithsScreen(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      body: Consumer<HadithProvider>(
        builder: (context, hadithProvider, child) {
          if (hadithProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (hadithProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    hadithProvider.errorMessage!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      hadithProvider.clearError();
                      hadithProvider.initialize();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                _buildHadithOfTheDay(context, hadithProvider),
                _buildCategoriesList(context, hadithProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHadithOfTheDay(BuildContext context, HadithProvider hadithProvider) {
    final hadithOfTheDay = hadithProvider.hadithOfTheDay;

    if (hadithOfTheDay == null) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.star,
                  color: Colors.amber,
                ),
                SizedBox(width: 8),
                Text(
                  'حديث اليوم',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              hadithOfTheDay.hadith.arabicText,
              style: const TextStyle(
                fontSize: 18,
                height: 1.8,
              ),
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: 12),
            Text(
              hadithOfTheDay.hadith.translation,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '- ${hadithOfTheDay.hadith.narrator}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Chip(
                  label: Text(hadithOfTheDay.hadith.source),
                  backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                ),
                const SizedBox(width: 8),
                Chip(
                  label: Text(hadithOfTheDay.hadith.grade),
                  backgroundColor: _getGradeColor(hadithOfTheDay.hadith.grade),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesList(BuildContext context, HadithProvider hadithProvider) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: hadithProvider.categories.length,
      itemBuilder: (context, index) {
        final category = hadithProvider.categories[index];
        return _buildCategoryCard(context, category, hadithProvider);
      },
    );
  }

  Widget _buildCategoryCard(
    BuildContext context,
    category,
    HadithProvider hadithProvider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          hadithProvider.setCurrentCategory(category);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HadithCategoryScreen(category: category),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getCategoryIcon(category.icon),
                  color: Theme.of(context).primaryColor,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      category.description,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${category.hadiths.length} حديث',
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'ethics':
        return Icons.favorite;
      case 'prayer':
        return Icons.place;
      case 'fasting':
        return Icons.brightness_3;
      case 'parents':
        return Icons.family_restroom;
      case 'dhikr':
        return Icons.radio_button_checked;
      case 'afterlife':
        return Icons.cloud;
      default:
        return Icons.article;
    }
  }

  Color _getGradeColor(String grade) {
    switch (grade) {
      case 'صحيح':
        return Colors.green.withValues(alpha: 0.2);
      case 'حسن':
        return Colors.blue.withValues(alpha: 0.2);
      case 'ضعيف':
        return Colors.orange.withValues(alpha: 0.2);
      default:
        return Colors.grey.withValues(alpha: 0.2);
    }
  }
}

class HadithCategoryScreen extends StatelessWidget {
  final dynamic category;

  const HadithCategoryScreen({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(category.name),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: category.hadiths.length,
        itemBuilder: (context, index) {
          final hadith = category.hadiths[index];
          return _buildHadithCard(context, hadith);
        },
      ),
    );
  }

  Widget _buildHadithCard(BuildContext context, hadith) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              hadith.arabicText,
              style: const TextStyle(
                fontSize: 16,
                height: 1.8,
              ),
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: 12),
            Text(
              hadith.translation,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '- ${hadith.narrator}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Chip(
                  label: Text(hadith.source),
                  backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                ),
                const SizedBox(width: 8),
                Chip(
                  label: Text(hadith.grade),
                  backgroundColor: _getGradeColor(hadith.grade),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(
                    hadith.isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: hadith.isFavorite ? Colors.red : null,
                  ),
                  onPressed: () {
                    Provider.of<HadithProvider>(context, listen: false)
                        .toggleFavorite(hadith);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getGradeColor(String grade) {
    switch (grade) {
      case 'صحيح':
        return Colors.green.withValues(alpha: 0.2);
      case 'حسن':
        return Colors.blue.withValues(alpha: 0.2);
      case 'ضعيف':
        return Colors.orange.withValues(alpha: 0.2);
      default:
        return Colors.grey.withValues(alpha: 0.2);
    }
  }
}

class FavoriteHadithsScreen extends StatelessWidget {
  const FavoriteHadithsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأحاديث المفضلة'),
      ),
      body: Consumer<HadithProvider>(
        builder: (context, hadithProvider, child) {
          final favoriteHadiths = hadithProvider.favoriteHadiths;

          if (favoriteHadiths.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.favorite_border,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'لا توجد أحاديث مفضلة',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: favoriteHadiths.length,
            itemBuilder: (context, index) {
              final hadith = favoriteHadiths[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hadith.arabicText,
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.8,
                        ),
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        hadith.translation,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '- ${hadith.narrator}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}

// Search Delegate for Hadith
class HadithSearchDelegate extends SearchDelegate {
  final HadithProvider hadithProvider;
  HadithSearchDelegate({required this.hadithProvider});

  @override
  String get searchFieldLabel => 'ابحث عن حديث أو راوي أو كلمة...';

  @override
  List<Widget>? buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget? buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    final results = _searchHadiths(query);
    if (results.isEmpty) {
      return const Center(child: Text('لا توجد نتائج للبحث.'));
    }
    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final hadith = results[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text(hadith.arabicText, textAlign: TextAlign.right),
            subtitle: Text(hadith.translation),
            trailing: IconButton(
              icon: Icon(hadith.isFavorite ? Icons.favorite : Icons.favorite_border, color: hadith.isFavorite ? Colors.red : null),
              onPressed: () {
                hadithProvider.toggleFavorite(hadith);
                showResults(context);
              },
            ),
          ),
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    final suggestions = _searchHadiths(query);
    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        final hadith = suggestions[index];
        return ListTile(
          title: Text(hadith.arabicText, textAlign: TextAlign.right),
          subtitle: Text(hadith.translation),
          onTap: () => showResults(context),
        );
      },
    );
  }

  List<dynamic> _searchHadiths(String query) {
    if (query.isEmpty) return [];
    final List<dynamic> allHadiths = hadithProvider.categories.expand((cat) => cat.hadiths).toList();
    return allHadiths.where((hadith) =>
      hadith.arabicText.contains(query) ||
      hadith.translation.contains(query) ||
      hadith.narrator.contains(query) ||
      (hadith.keywords != null && hadith.keywords.any((k) => k.contains(query)))
    ).toList();
  }
}
