import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../models/hadith_models.dart';

class HadithApiService {
  // Using multiple sources for hadith data
  static const String _hadithApiBaseUrl = 'https://api.hadith.gading.dev';
  // Note: Sunnah API reserved for future implementation
  // static const String _sunnhApiBaseUrl = 'https://api.sunnah.com/v1';

  // Cache for hadith data
  static List<HadithCategory>? _cachedHadiths;

  // Get all hadith categories
  Future<List<HadithCategory>> getAllHadithCategories() async {
    if (_cachedHadiths != null) {
      return _cachedHadiths!;
    }

    try {
      // Try to get from hadith API
      final categories = await _getHadithFromGadingApi();
      if (categories.isNotEmpty) {
        _cachedHadiths = categories;
        return categories;
      }

      // Fallback to default data
      _cachedHadiths = _getDefaultHadiths();
      return _cachedHadiths!;
    } catch (e) {
      _cachedHadiths = _getDefaultHadiths();
      return _cachedHadiths!;
    }
  }

  // Get hadith from Gading API with improved error handling
  Future<List<HadithCategory>> _getHadithFromGadingApi() async {
    List<HadithCategory> categories = [];

    // List of available books with more hadith per book
    final books = ['bukhari', 'muslim', 'abudawud', 'tirmidhi', 'nasai', 'ibnmajah'];

    for (String book in books) {
      try {
        // Get more hadiths per book (1-50 instead of 1-10)
        final response = await http.get(
          Uri.parse('$_hadithApiBaseUrl/books/$book?range=1-50'),
          headers: {'Accept': 'application/json'},
        ).timeout(const Duration(seconds: 15));

        if (response.statusCode == 200) {
          final data = json.decode(response.body);

          // Handle different API response structures
          List<dynamic> hadiths = [];
          if (data['data'] != null && data['data']['hadiths'] != null) {
            hadiths = data['data']['hadiths'] as List;
          } else if (data['hadiths'] != null) {
            hadiths = data['hadiths'] as List;
          } else if (data is List) {
            hadiths = data;
          }

          if (hadiths.isNotEmpty) {
            List<Hadith> hadithList = [];

            for (var hadithData in hadiths) {
              if (hadithData != null) {
                hadithList.add(Hadith(
                  id: hadithData['number'] ?? hadithData['id'] ?? hadithList.length + 1,
                  arabicText: hadithData['arab'] ?? hadithData['arabic'] ?? hadithData['text'] ?? '',
                  translation: hadithData['id'] ?? hadithData['translation'] ?? '',
                  narrator: _extractNarrator(hadithData['arab'] ?? hadithData['arabic'] ?? ''),
                  source: _getBookName(book),
                  grade: hadithData['grade'] ?? 'صحيح',
                  explanation: hadithData['explanation'],
                  keywords: _extractKeywords(hadithData['arab'] ?? hadithData['arabic'] ?? ''),
                  isFavorite: false,
                ));
              }
            }

            if (hadithList.isNotEmpty) {
              categories.add(HadithCategory(
                id: categories.length + 1,
                name: _getBookName(book),
                description: _getBookDescription(book),
                icon: 'book',
                hadiths: hadithList,
              ));
            }
          }
        }
      } catch (e) {
        // Log error but continue with next book
        print('Error fetching $book: $e');
        continue;
      }

      // Small delay between requests to be respectful to the API
      await Future.delayed(const Duration(milliseconds: 500));
    }

    return categories;
  }

  // Extract narrator from hadith text
  String _extractNarrator(String arabicText) {
    // Simple extraction - look for common narrator patterns
    if (arabicText.contains('أبي هريرة')) return 'أبو هريرة رضي الله عنه';
    if (arabicText.contains('عائشة')) return 'عائشة رضي الله عنها';
    if (arabicText.contains('ابن عمر')) return 'عبد الله بن عمر رضي الله عنهما';
    if (arabicText.contains('أنس')) return 'أنس بن مالك رضي الله عنه';
    if (arabicText.contains('جابر')) return 'جابر بن عبد الله رضي الله عنه';
    return 'راوي معتبر';
  }

  // Extract keywords from hadith text
  List<String> _extractKeywords(String arabicText) {
    List<String> keywords = [];

    // Common Islamic terms
    final terms = [
      'الصلاة', 'الصيام', 'الزكاة', 'الحج', 'الجهاد',
      'الإيمان', 'الإسلام', 'الإحسان', 'التوبة', 'الذكر',
      'الدعاء', 'القرآن', 'السنة', 'الجنة', 'النار',
      'الوالدين', 'الأخلاق', 'الصدق', 'الأمانة', 'العدل'
    ];

    for (String term in terms) {
      if (arabicText.contains(term)) {
        keywords.add(term);
      }
    }

    return keywords.isEmpty ? ['حديث'] : keywords;
  }

  // Get book name in Arabic
  String _getBookName(String bookKey) {
    switch (bookKey.toLowerCase()) {
      case 'bukhari':
        return 'صحيح البخاري';
      case 'muslim':
        return 'صحيح مسلم';
      case 'abudawud':
        return 'سنن أبي داود';
      case 'tirmidhi':
        return 'سنن الترمذي';
      case 'nasai':
        return 'سنن النسائي';
      case 'ibnmajah':
        return 'سنن ابن ماجه';
      default:
        return 'كتاب الحديث';
    }
  }

  // Get book description
  String _getBookDescription(String bookKey) {
    switch (bookKey.toLowerCase()) {
      case 'bukhari':
        return 'أصح كتاب بعد كتاب الله - الإمام البخاري';
      case 'muslim':
        return 'ثاني أصح الكتب بعد البخاري - الإمام مسلم';
      case 'abudawud':
        return 'من كتب السنن المعتبرة - الإمام أبو داود';
      case 'tirmidhi':
        return 'من كتب السنن المشهورة - الإمام الترمذي';
      case 'nasai':
        return 'من كتب السنن الستة - الإمام النسائي';
      case 'ibnmajah':
        return 'من كتب السنن الستة - الإمام ابن ماجه';
      default:
        return 'مجموعة أحاديث نبوية صحيحة';
    }
  }

  // Search hadith
  Future<List<Hadith>> searchHadith(String query) async {
    final categories = await getAllHadithCategories();
    List<Hadith> results = [];

    for (var category in categories) {
      for (var hadith in category.hadiths) {
        if (hadith.arabicText.contains(query) ||
            hadith.translation.contains(query) ||
            hadith.narrator.contains(query) ||
            hadith.keywords.any((keyword) => keyword.contains(query))) {
          results.add(hadith);
        }
      }
    }

    return results;
  }

  // Get random hadith for "Hadith of the Day"
  Future<Hadith?> getRandomHadith() async {
    final categories = await getAllHadithCategories();

    if (categories.isEmpty) return null;

    final allHadiths = <Hadith>[];
    for (var category in categories) {
      allHadiths.addAll(category.hadiths);
    }

    if (allHadiths.isEmpty) return null;

    final random = Random();
    return allHadiths[random.nextInt(allHadiths.length)];
  }

  // Get hadith by category
  Future<List<Hadith>> getHadithByCategory(String categoryName) async {
    final categories = await getAllHadithCategories();

    for (var category in categories) {
      if (category.name.contains(categoryName)) {
        return category.hadiths;
      }
    }

    return [];
  }

  // Default hadith data if API fails
  List<HadithCategory> _getDefaultHadiths() {
    return [
      HadithCategory(
        id: 1,
        name: 'الأخلاق والآداب',
        description: 'أحاديث عن الأخلاق الحسنة والآداب الإسلامية',
        icon: 'ethics',
        hadiths: [
          Hadith(
            id: 1,
            arabicText: 'إِنَّمَا بُعِثْتُ لِأُتَمِّمَ مَكَارِمَ الْأَخْلَاقِ',
            translation: 'إنما بُعثت لأتمم مكارم الأخلاق',
            narrator: 'أبو هريرة رضي الله عنه',
            source: 'الأدب المفرد للبخاري',
            grade: 'صحيح',
            keywords: ['أخلاق', 'مكارم', 'بعثة'],
            explanation: 'يبين هذا الحديث أن الهدف الأساسي من بعثة النبي صلى الله عليه وسلم هو إتمام مكارم الأخلاق',
          ),
          Hadith(
            id: 2,
            arabicText: 'الْمُؤْمِنُ لَيْسَ بِالطَّعَّانِ وَلَا اللَّعَّانِ وَلَا الْفَاحِشِ وَلَا الْبَذِيءِ',
            translation: 'المؤمن ليس بالطعان ولا اللعان ولا الفاحش ولا البذيء',
            narrator: 'عبد الله بن مسعود رضي الله عنه',
            source: 'سنن الترمذي',
            grade: 'صحيح',
            keywords: ['مؤمن', 'أخلاق', 'لسان'],
            explanation: 'يوضح هذا الحديث صفات المؤمن الحق وأنه يتجنب سوء الكلام',
          ),
        ],
      ),
      HadithCategory(
        id: 2,
        name: 'الصلاة',
        description: 'أحاديث عن الصلاة وأحكامها',
        icon: 'prayer',
        hadiths: [
          Hadith(
            id: 3,
            arabicText: 'الصَّلَاةُ عِمَادُ الدِّينِ',
            translation: 'الصلاة عماد الدين',
            narrator: 'عمر بن الخطاب رضي الله عنه',
            source: 'البيهقي',
            grade: 'حسن',
            keywords: ['صلاة', 'عماد', 'دين'],
            explanation: 'يبين هذا الحديث أهمية الصلاة في الإسلام وأنها أساس الدين',
          ),
        ],
      ),
      HadithCategory(
        id: 3,
        name: 'الذكر والدعاء',
        description: 'أحاديث عن فضل الذكر والدعاء',
        icon: 'dhikr',
        hadiths: [
          Hadith(
            id: 4,
            arabicText: 'أَلَا أُنَبِّئُكُمْ بِخَيْرِ أَعْمَالِكُمْ وَأَزْكَاهَا عِنْدَ مَلِيكِكُمْ؟ ذِكْرُ اللَّهِ',
            translation: 'ألا أنبئكم بخير أعمالكم وأزكاها عند مليككم؟ ذكر الله',
            narrator: 'أبو الدرداء رضي الله عنه',
            source: 'سنن الترمذي',
            grade: 'حسن',
            keywords: ['ذكر', 'خير', 'أعمال'],
            explanation: 'يوضح هذا الحديث فضل ذكر الله وأنه من أفضل الأعمال',
          ),
        ],
      ),
    ];
  }

  // Clear cache
  void clearCache() {
    _cachedHadiths = null;
  }

  // Get hadith statistics
  Future<Map<String, int>> getHadithStatistics() async {
    final categories = await getAllHadithCategories();

    int totalHadiths = 0;
    Map<String, int> sourceCount = {};
    Map<String, int> gradeCount = {};

    for (var category in categories) {
      totalHadiths += category.hadiths.length;

      for (var hadith in category.hadiths) {
        sourceCount[hadith.source] = (sourceCount[hadith.source] ?? 0) + 1;
        gradeCount[hadith.grade] = (gradeCount[hadith.grade] ?? 0) + 1;
      }
    }

    return {
      'total': totalHadiths,
      'categories': categories.length,
      'sahih': gradeCount['صحيح'] ?? 0,
      'hasan': gradeCount['حسن'] ?? 0,
    };
  }
}
