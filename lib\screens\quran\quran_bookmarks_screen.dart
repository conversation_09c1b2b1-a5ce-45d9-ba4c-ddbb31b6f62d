import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../providers/quran_provider.dart';
import '../../models/quran_models.dart';

class QuranBookmarksScreen extends StatelessWidget {
  const QuranBookmarksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العلامات المرجعية'),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            onPressed: () => _showClearAllDialog(context),
          ),
        ],
      ),
      body: Consumer<QuranProvider>(
        builder: (context, quranProvider, child) {
          final bookmarks = quranProvider.bookmarks;

          if (bookmarks.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.bookmark_border,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'لا توجد علامات مرجعية',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'اضغط على أيقونة العلامة المرجعية في أي آية لحفظها هنا',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: bookmarks.length,
            itemBuilder: (context, index) {
              final bookmark = bookmarks[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${bookmark.surahName} - آية ${bookmark.ayahNumber}',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                          const Spacer(),
                          Text(
                            _formatDate(bookmark.createdAt),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _showDeleteDialog(
                              context,
                              bookmark,
                              quranProvider,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        bookmark.ayahText,
                        style: const TextStyle(
                          fontSize: 18,
                          height: 1.8,
                          fontFamily: 'Amiri',
                        ),
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.justify,
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: () {
                              // Navigate to the specific ayah
                              final quranProvider = Provider.of<QuranProvider>(context, listen: false);
                              quranProvider.setCurrentSurah(quranProvider.surahs.firstWhere(
                                (s) => s.number == bookmark.surahNumber
                              ));

                              // Load full surah data if needed
                              quranProvider.loadSurah(bookmark.surahNumber).then((surah) {
                                if (context.mounted && surah != null) {
                                  // Set the current ayah for highlighting
                                  final ayah = surah.ayahs.firstWhere(
                                    (a) => a.numberInSurah == bookmark.ayahNumber
                                  );
                                  quranProvider.setCurrentAyah(ayah);

                                  // Navigate to the surah reading screen
                                  Navigator.pushNamed(
                                    context,
                                    '/quran/surah',
                                    arguments: {
                                      'surah': surah,
                                      'initialAyah': bookmark.ayahNumber,
                                    },
                                  );
                                }
                              });
                            },
                            icon: const Icon(Icons.visibility),
                            label: const Text('عرض الآية'),
                          ),
                          const SizedBox(width: 8),
                          OutlinedButton.icon(
                            onPressed: () {
                              quranProvider.playAyah(
                                bookmark.surahNumber,
                                bookmark.ayahNumber,
                              );
                            },
                            icon: const Icon(Icons.play_arrow),
                            label: const Text('تشغيل'),
                          ),
                          const Spacer(),
                          IconButton(
                            onPressed: () {
                              final shareText = '''${bookmark.ayahText}

الآية ${bookmark.ayahNumber} من سورة ${bookmark.surahName}

من تطبيق قرآني 📱''';
                              Share.share(shareText);
                            },
                            icon: const Icon(Icons.share),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return 'الآن';
        }
        return 'منذ ${difference.inMinutes} دقيقة';
      }
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showDeleteDialog(
    BuildContext context,
    BookmarkModel bookmark,
    QuranProvider quranProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العلامة المرجعية'),
        content: Text(
          'هل تريد حذف العلامة المرجعية من ${bookmark.surahName} - آية ${bookmark.ayahNumber}؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              await quranProvider.removeBookmark(
                bookmark.surahNumber,
                bookmark.ayahNumber,
              );
              if (context.mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف العلامة المرجعية'),
                  ),
                );
              }
            },
            child: const Text(
              'حذف',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    final bookmarksCount = quranProvider.bookmarks.length;

    if (bookmarksCount == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد علامات مرجعية للحذف'),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف جميع العلامات المرجعية'),
        content: Text(
          'هل تريد حذف جميع العلامات المرجعية ($bookmarksCount علامة)؟\nلا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              // Remove all bookmarks one by one
              final bookmarks = List.from(quranProvider.bookmarks);
              for (final bookmark in bookmarks) {
                await quranProvider.removeBookmark(
                  bookmark.surahNumber,
                  bookmark.ayahNumber,
                );
              }

              if (context.mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف جميع العلامات المرجعية'),
                  ),
                );
              }
            },
            child: const Text(
              'حذف الكل',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
