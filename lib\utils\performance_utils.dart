import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class PerformanceUtils {
  /// قياس وقت تنفيذ دالة معينة
  static Future<T> measurePerformance<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      stopwatch.stop();
      
      if (kDebugMode) {
        print('⏱️ $operationName took ${stopwatch.elapsedMilliseconds}ms');
      }
      
      return result;
    } catch (e) {
      stopwatch.stop();
      if (kDebugMode) {
        print('❌ $operationName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      }
      rethrow;
    }
  }

  /// قياس وقت تنفيذ دالة متزامنة
  static T measureSyncPerformance<T>(
    String operationName,
    T Function() operation,
  ) {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = operation();
      stopwatch.stop();
      
      if (kDebugMode) {
        print('⏱️ $operationName took ${stopwatch.elapsedMilliseconds}ms');
      }
      
      return result;
    } catch (e) {
      stopwatch.stop();
      if (kDebugMode) {
        print('❌ $operationName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      }
      rethrow;
    }
  }

  /// تسجيل استهلاك الذاكرة (تقريبي)
  static void logMemoryUsage(String context) {
    if (kDebugMode) {
      // في Flutter، لا يمكن الحصول على استهلاك الذاكرة بدقة
      // لكن يمكن تسجيل نقاط مرجعية للمراقبة
      print('💾 Memory checkpoint: $context at ${DateTime.now()}');
    }
  }

  /// تحسين بناء الـ widgets
  static Widget optimizedBuilder({
    required Widget Function() builder,
    String? debugLabel,
  }) {
    return Builder(
      builder: (context) {
        if (kDebugMode && debugLabel != null) {
          return measureSyncPerformance(
            'Building $debugLabel',
            builder,
          );
        }
        return builder();
      },
    );
  }

  /// تحسين ListView للأداء
  static Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    EdgeInsets? padding,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      // تحسينات الأداء
      cacheExtent: 500, // تحميل مسبق للعناصر
      addAutomaticKeepAlives: false, // توفير الذاكرة
      addRepaintBoundaries: false, // تقليل إعادة الرسم غير الضرورية
    );
  }

  /// تحسين الصور للأداء
  static Widget optimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      // تحسينات الأداء
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
      filterQuality: FilterQuality.medium,
      errorBuilder: errorWidget != null 
          ? (context, error, stackTrace) => errorWidget
          : (context, error, stackTrace) => Container(
              width: width,
              height: height,
              color: Colors.grey[300],
              child: const Icon(Icons.image_not_supported),
            ),
      frameBuilder: placeholder != null
          ? (context, child, frame, wasSynchronouslyLoaded) {
              if (wasSynchronouslyLoaded) return child;
              return frame == null ? placeholder : child;
            }
          : null,
    );
  }

  /// تحسين الانتقالات بين الصفحات
  static PageRouteBuilder optimizedPageRoute({
    required Widget page,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final tween = Tween(begin: 0.0, end: 1.0).chain(
          CurveTween(curve: curve),
        );
        
        return FadeTransition(
          opacity: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  /// تحسين الـ FutureBuilder
  static Widget optimizedFutureBuilder<T>({
    required Future<T> future,
    required Widget Function(BuildContext, T) builder,
    Widget? loadingWidget,
    Widget Function(BuildContext, Object)? errorBuilder,
    String? debugLabel,
  }) {
    return FutureBuilder<T>(
      future: future,
      builder: (context, snapshot) {
        if (kDebugMode && debugLabel != null) {
          logMemoryUsage('FutureBuilder $debugLabel - ${snapshot.connectionState}');
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return loadingWidget ?? const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          if (errorBuilder != null) {
            return errorBuilder(context, snapshot.error!);
          }
          return Center(
            child: Text('خطأ: ${snapshot.error}'),
          );
        }

        if (snapshot.hasData) {
          return builder(context, snapshot.data as T);
        }

        return const SizedBox.shrink();
      },
    );
  }

  /// تحسين الـ StreamBuilder
  static Widget optimizedStreamBuilder<T>({
    required Stream<T> stream,
    required Widget Function(BuildContext, T) builder,
    Widget? loadingWidget,
    Widget Function(BuildContext, Object)? errorBuilder,
    String? debugLabel,
  }) {
    return StreamBuilder<T>(
      stream: stream,
      builder: (context, snapshot) {
        if (kDebugMode && debugLabel != null) {
          logMemoryUsage('StreamBuilder $debugLabel - ${snapshot.connectionState}');
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return loadingWidget ?? const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          if (errorBuilder != null) {
            return errorBuilder(context, snapshot.error!);
          }
          return Center(
            child: Text('خطأ: ${snapshot.error}'),
          );
        }

        if (snapshot.hasData) {
          return builder(context, snapshot.data as T);
        }

        return const SizedBox.shrink();
      },
    );
  }

  /// تحسين الـ AnimationController
  static AnimationController optimizedAnimationController({
    required TickerProvider vsync,
    Duration duration = const Duration(milliseconds: 300),
    String? debugLabel,
  }) {
    final controller = AnimationController(
      duration: duration,
      vsync: vsync,
      debugLabel: debugLabel,
    );

    if (kDebugMode && debugLabel != null) {
      controller.addStatusListener((status) {
        print('🎬 Animation $debugLabel: $status');
      });
    }

    return controller;
  }

  /// تنظيف الموارد
  static void disposeResources(List<dynamic> resources) {
    for (final resource in resources) {
      try {
        if (resource is AnimationController) {
          resource.dispose();
        } else if (resource is TextEditingController) {
          resource.dispose();
        } else if (resource is ScrollController) {
          resource.dispose();
        } else if (resource is PageController) {
          resource.dispose();
        } else if (resource is TabController) {
          resource.dispose();
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Error disposing resource: $e');
        }
      }
    }
  }

  /// تحسين الـ Debouncing للبحث
  static void debounce({
    required String key,
    required Duration delay,
    required VoidCallback action,
  }) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, action);
  }

  static final Map<String, Timer> _debounceTimers = {};

  /// تنظيف جميع الـ timers
  static void clearAllTimers() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
  }
}

/// Timer class للـ debouncing
class Timer {
  final Duration duration;
  final VoidCallback callback;
  bool _isActive = true;

  Timer(this.duration, this.callback) {
    Future.delayed(duration, () {
      if (_isActive) {
        callback();
      }
    });
  }

  void cancel() {
    _isActive = false;
  }
}

/// Widget للمساعدة في تحسين الأداء
class PerformanceWidget extends StatefulWidget {
  final Widget child;
  final String? debugLabel;

  const PerformanceWidget({
    super.key,
    required this.child,
    this.debugLabel,
  });

  @override
  State<PerformanceWidget> createState() => _PerformanceWidgetState();
}

class _PerformanceWidgetState extends State<PerformanceWidget> {
  @override
  void initState() {
    super.initState();
    if (kDebugMode && widget.debugLabel != null) {
      PerformanceUtils.logMemoryUsage('${widget.debugLabel} - initState');
    }
  }

  @override
  void dispose() {
    if (kDebugMode && widget.debugLabel != null) {
      PerformanceUtils.logMemoryUsage('${widget.debugLabel} - dispose');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}