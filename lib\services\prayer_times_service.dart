import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';

class PrayerTimesService {
  static const String _baseUrl = 'https://api.aladhan.com/v1';

  // Get prayer times for current location with fallback
  Future<Map<String, dynamic>?> getPrayerTimes({
    required double latitude,
    required double longitude,
    int method = 4, // 4 = Umm Al-Qura University, Makkah
  }) async {
    try {
      final now = DateTime.now();
      final dateString = '${now.day.toString().padLeft(2, '0')}-${now.month.toString().padLeft(2, '0')}-${now.year}';

      final response = await http.get(
        Uri.parse('$_baseUrl/timings/$dateString?latitude=$latitude&longitude=$longitude&method=$method'),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data'];
      }

      // Fallback to default location (Mecca) if current location fails
      return await _getFallbackPrayerTimes(method);
    } catch (e) {
      // Fallback to default location (Mecca) if error occurs
      return await _getFallbackPrayerTimes(method);
    }
  }

  // Fallback prayer times for Mecca
  Future<Map<String, dynamic>?> _getFallbackPrayerTimes(int method) async {
    try {
      final now = DateTime.now();
      final dateString = '${now.day.toString().padLeft(2, '0')}-${now.month.toString().padLeft(2, '0')}-${now.year}';

      // Mecca coordinates: 21.4225, 39.8262
      final response = await http.get(
        Uri.parse('$_baseUrl/timings/$dateString?latitude=21.4225&longitude=39.8262&method=$method'),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data'];
      }

      // If even fallback fails, return default times
      return _getDefaultPrayerTimes();
    } catch (e) {
      return _getDefaultPrayerTimes();
    }
  }

  // Default prayer times if all APIs fail
  Map<String, dynamic> _getDefaultPrayerTimes() {
    return {
      'timings': {
        'Fajr': '05:30',
        'Dhuhr': '12:30',
        'Asr': '15:45',
        'Maghrib': '18:15',
        'Isha': '19:45',
      },
      'date': {
        'readable': DateTime.now().toString().split(' ')[0],
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      },
      'meta': {
        'latitude': 21.4225,
        'longitude': 39.8262,
        'timezone': 'Asia/Riyadh',
        'method': {
          'id': 4,
          'name': 'Umm Al-Qura University, Makkah',
        },
      },
    };
  }

  // Get prayer times by city
  Future<Map<String, dynamic>?> getPrayerTimesByCity({
    required String city,
    required String country,
    int method = 4,
  }) async {
    try {
      final now = DateTime.now();
      final dateString = '${now.day.toString().padLeft(2, '0')}-${now.month.toString().padLeft(2, '0')}-${now.year}';

      final response = await http.get(
        Uri.parse('$_baseUrl/timingsByCity/$dateString?city=$city&country=$country&method=$method'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data'];
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Get monthly prayer times
  Future<List<Map<String, dynamic>>> getMonthlyPrayerTimes({
    required double latitude,
    required double longitude,
    int? month,
    int? year,
    int method = 4,
  }) async {
    try {
      final now = DateTime.now();
      final targetMonth = month ?? now.month;
      final targetYear = year ?? now.year;

      final response = await http.get(
        Uri.parse('$_baseUrl/calendar/$targetYear/$targetMonth?latitude=$latitude&longitude=$longitude&method=$method'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['data'] ?? []);
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  // Parse prayer times to TimeOfDay objects
  Map<String, TimeOfDay> parsePrayerTimes(Map<String, dynamic> timingsData) {
    final timings = timingsData['timings'] as Map<String, dynamic>;

    return {
      'Fajr': _parseTime(timings['Fajr']),
      'Dhuhr': _parseTime(timings['Dhuhr']),
      'Asr': _parseTime(timings['Asr']),
      'Maghrib': _parseTime(timings['Maghrib']),
      'Isha': _parseTime(timings['Isha']),
    };
  }

  // Parse prayer times with Arabic names
  Map<String, TimeOfDay> parsePrayerTimesArabic(Map<String, dynamic> timingsData) {
    final timings = timingsData['timings'] as Map<String, dynamic>;

    return {
      'الفجر': _parseTime(timings['Fajr']),
      'الظهر': _parseTime(timings['Dhuhr']),
      'العصر': _parseTime(timings['Asr']),
      'المغرب': _parseTime(timings['Maghrib']),
      'العشاء': _parseTime(timings['Isha']),
    };
  }

  // Helper method to parse time string to TimeOfDay
  TimeOfDay _parseTime(String timeString) {
    try {
      // Remove timezone info if present
      final cleanTime = timeString.split(' ')[0];
      final parts = cleanTime.split(':');

      if (parts.length >= 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        return TimeOfDay(hour: hour, minute: minute);
      }
    } catch (e) {
      // Return default time if parsing fails
    }
    return const TimeOfDay(hour: 0, minute: 0);
  }

  // Get next prayer time
  String getNextPrayer(Map<String, TimeOfDay> prayerTimes) {
    final now = TimeOfDay.now();
    final currentMinutes = now.hour * 60 + now.minute;

    final prayers = [
      {'name': 'الفجر', 'time': prayerTimes['الفجر']!},
      {'name': 'الظهر', 'time': prayerTimes['الظهر']!},
      {'name': 'العصر', 'time': prayerTimes['العصر']!},
      {'name': 'المغرب', 'time': prayerTimes['المغرب']!},
      {'name': 'العشاء', 'time': prayerTimes['العشاء']!},
    ];

    for (var prayer in prayers) {
      final prayerTime = prayer['time'] as TimeOfDay;
      final prayerMinutes = prayerTime.hour * 60 + prayerTime.minute;

      if (prayerMinutes > currentMinutes) {
        return prayer['name'] as String;
      }
    }

    // If no prayer is left today, next is Fajr tomorrow
    return 'الفجر';
  }

  // Get time remaining until next prayer
  Duration getTimeUntilNextPrayer(Map<String, TimeOfDay> prayerTimes) {
    final now = DateTime.now();
    final nextPrayerName = getNextPrayer(prayerTimes);
    final nextPrayerTime = prayerTimes[nextPrayerName]!;

    var nextPrayerDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      nextPrayerTime.hour,
      nextPrayerTime.minute,
    );

    // If the prayer time has passed today, it's tomorrow
    if (nextPrayerDateTime.isBefore(now)) {
      nextPrayerDateTime = nextPrayerDateTime.add(const Duration(days: 1));
    }

    return nextPrayerDateTime.difference(now);
  }

  // Format time for display
  String formatTime(TimeOfDay time, {bool use24Hour = false}) {
    if (use24Hour) {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else {
      final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
      final period = time.period == DayPeriod.am ? 'ص' : 'م';
      return '${hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')} $period';
    }
  }

  // Get available calculation methods
  List<Map<String, dynamic>> getCalculationMethods() {
    return [
      {'id': 1, 'name': 'جامعة العلوم الإسلامية، كراتشي', 'nameEn': 'University of Islamic Sciences, Karachi'},
      {'id': 2, 'name': 'الرابطة الإسلامية لأمريكا الشمالية', 'nameEn': 'Islamic Society of North America'},
      {'id': 3, 'name': 'رابطة العالم الإسلامي', 'nameEn': 'Muslim World League'},
      {'id': 4, 'name': 'جامعة أم القرى، مكة المكرمة', 'nameEn': 'Umm Al-Qura University, Makkah'},
      {'id': 5, 'name': 'الهيئة المصرية العامة للمساحة', 'nameEn': 'Egyptian General Authority of Survey'},
      {'id': 7, 'name': 'معهد الجيوفيزياء، جامعة طهران', 'nameEn': 'Institute of Geophysics, University of Tehran'},
      {'id': 8, 'name': 'الخليج', 'nameEn': 'Gulf Region'},
      {'id': 9, 'name': 'الكويت', 'nameEn': 'Kuwait'},
      {'id': 10, 'name': 'قطر', 'nameEn': 'Qatar'},
      {'id': 11, 'name': 'مجلس الإفتاء الأعلى، سنغافورة', 'nameEn': 'Majlis Ugama Islam Singapura, Singapore'},
      {'id': 12, 'name': 'اتحاد المنظمات الإسلامية في فرنسا', 'nameEn': 'Union Organization islamic de France'},
      {'id': 13, 'name': 'ديانت، تركيا', 'nameEn': 'Diyanet İşleri Başkanlığı, Turkey'},
      {'id': 14, 'name': 'المجلس الروحي لمسلمي روسيا', 'nameEn': 'Spiritual Administration of Muslims of Russia'},
    ];
  }

  // Get Qibla direction
  Future<double?> getQiblaDirection({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/qibla/$latitude/$longitude'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data']['direction']?.toDouble();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Get Islamic date
  Future<Map<String, dynamic>?> getIslamicDate() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/gToH'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data']['hijri'];
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Check if it's prayer time now (within 5 minutes)
  bool isPrayerTimeNow(Map<String, TimeOfDay> prayerTimes, {int toleranceMinutes = 5}) {
    final now = TimeOfDay.now();
    final currentMinutes = now.hour * 60 + now.minute;

    for (var prayerTime in prayerTimes.values) {
      final prayerMinutes = prayerTime.hour * 60 + prayerTime.minute;
      final difference = (currentMinutes - prayerMinutes).abs();

      if (difference <= toleranceMinutes) {
        return true;
      }
    }

    return false;
  }
}
