org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
android.overridePathCheck=true

# Kotlin compiler settings
kotlin.code.style=official
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.incremental.js=true
kotlin.incremental.js.klib=true
kotlin.incremental.multiplatform=true
kotlin.incremental.usePreciseJavaTracking=true
kotlin.parallel.tasks.in.project=true

# Gradle daemon settings
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# Android build optimizations
android.nonTransitiveRClass=true
android.nonFinalResIds=true
android.enableR8.fullMode=true
