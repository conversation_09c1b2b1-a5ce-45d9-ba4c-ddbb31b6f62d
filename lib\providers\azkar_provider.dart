import 'package:flutter/material.dart';
import '../models/azkar_models.dart';
import '../services/database_service.dart';
import '../services/azkar_api_service.dart';

class AzkarProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService.instance;
  final AzkarApiService _azkarApiService = AzkarApiService();

  List<ZikrCategory> _categories = [];
  final Map<int, ZikrProgress> _progressMap = {};
  List<TasbihCounter> _tasbihCounters = [];

  bool _isLoading = false;
  String? _errorMessage;

  // Current zikr being read
  ZikrCategory? _currentCategory;
  Zikr? _currentZikr;
  int _currentZikrIndex = 0;

  // Getters
  List<ZikrCategory> get categories => _categories;
  Map<int, ZikrProgress> get progressMap => _progressMap;
  List<TasbihCounter> get tasbihCounters => _tasbihCounters;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  ZikrCategory? get currentCategory => _currentCategory;
  Zikr? get currentZikr => _currentZikr;
  int get currentZikrIndex => _currentZikrIndex;

  Future<void> initialize() async {
    setLoading(true);
    try {
      await _loadCategories();
      await _loadProgress();
      await _loadTasbihCounters();
    } catch (e) {
      _errorMessage = 'فشل في تحميل الأذكار: $e';
    } finally {
      setLoading(false);
    }
  }

  Future<void> _loadCategories() async {
    try {
      // Load azkar from API
      _categories = await _azkarApiService.getAllAzkarCategories();
    } catch (e) {
      // Fallback to default azkar
      _categories = AzkarData.getDefaultCategories();
    }
    notifyListeners();
  }

  Future<void> _loadProgress() async {
    _progressMap.clear();

    for (final category in _categories) {
      for (final zikr in category.azkar) {
        final progress = await _databaseService.getZikrProgress(zikr.id);
        if (progress != null) {
          _progressMap[zikr.id] = progress;
        }
      }
    }

    notifyListeners();
  }

  Future<void> _loadTasbihCounters() async {
    _tasbihCounters = await _databaseService.getAllTasbihCounters();
    notifyListeners();
  }

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setCurrentCategory(ZikrCategory category) {
    _currentCategory = category;
    _currentZikrIndex = 0;
    _currentZikr = category.azkar.isNotEmpty ? category.azkar.first : null;
    notifyListeners();
  }

  void setCurrentZikr(Zikr zikr) {
    _currentZikr = zikr;
    if (_currentCategory != null) {
      _currentZikrIndex = _currentCategory!.azkar.indexOf(zikr);
    }
    notifyListeners();
  }

  void nextZikr() {
    if (_currentCategory == null || _currentCategory!.azkar.isEmpty) return;

    _currentZikrIndex = (_currentZikrIndex + 1) % _currentCategory!.azkar.length;
    _currentZikr = _currentCategory!.azkar[_currentZikrIndex];
    notifyListeners();
  }

  void previousZikr() {
    if (_currentCategory == null || _currentCategory!.azkar.isEmpty) return;

    _currentZikrIndex = (_currentZikrIndex - 1 + _currentCategory!.azkar.length) % _currentCategory!.azkar.length;
    _currentZikr = _currentCategory!.azkar[_currentZikrIndex];
    notifyListeners();
  }

  // Progress methods
  ZikrProgress getZikrProgress(int zikrId) {
    return _progressMap[zikrId] ?? ZikrProgress(
      zikrId: zikrId,
      currentCount: 0,
      totalCount: _getZikrById(zikrId)?.count ?? 1,
      lastUpdated: DateTime.now(),
      isCompleted: false,
    );
  }

  Future<void> incrementZikrCount(int zikrId) async {
    final zikr = _getZikrById(zikrId);
    if (zikr == null) return;

    final currentProgress = getZikrProgress(zikrId);
    final newCount = currentProgress.currentCount + 1;
    final isCompleted = newCount >= zikr.count;

    final updatedProgress = ZikrProgress(
      zikrId: zikrId,
      currentCount: newCount,
      totalCount: zikr.count,
      lastUpdated: DateTime.now(),
      isCompleted: isCompleted,
    );

    _progressMap[zikrId] = updatedProgress;
    await _databaseService.saveZikrProgress(updatedProgress);

    notifyListeners();

    // Check if category is completed
    if (isCompleted) {
      _checkCategoryCompletion();
    }
  }

  Future<void> resetZikrProgress(int zikrId) async {
    await _databaseService.resetZikrProgress(zikrId);
    _progressMap.remove(zikrId);
    notifyListeners();
  }

  Future<void> resetCategoryProgress(int categoryId) async {
    final category = _categories.firstWhere((c) => c.id == categoryId);

    for (final zikr in category.azkar) {
      await resetZikrProgress(zikr.id);
    }
  }

  void _checkCategoryCompletion() {
    if (_currentCategory == null) return;

    final allCompleted = _currentCategory!.azkar.every((zikr) {
      final progress = getZikrProgress(zikr.id);
      return progress.isCompleted;
    });

    if (allCompleted) {
      // Category completed - could trigger notification or celebration
      _onCategoryCompleted(_currentCategory!);
    }
  }

  void _onCategoryCompleted(ZikrCategory category) {
    // This could trigger a notification or show a celebration dialog
    // Future enhancement: Add notification service call here
    // NotificationService.instance.showAzkarCompletionNotification(category.name);
  }

  Zikr? _getZikrById(int zikrId) {
    for (final category in _categories) {
      for (final zikr in category.azkar) {
        if (zikr.id == zikrId) return zikr;
      }
    }
    return null;
  }

  // Tasbih methods
  Future<void> createTasbihCounter(String name, String zikrText, int target) async {
    final counter = TasbihCounter(
      name: name,
      count: 0,
      target: target,
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
      zikrText: zikrText,
    );

    await _databaseService.saveTasbihCounter(name, counter);
    await _loadTasbihCounters();
  }

  Future<void> incrementTasbihCount(String name) async {
    final counter = _tasbihCounters.firstWhere((c) => c.name == name);

    final updatedCounter = TasbihCounter(
      name: counter.name,
      count: counter.count + 1,
      target: counter.target,
      createdAt: counter.createdAt,
      lastUpdated: DateTime.now(),
      zikrText: counter.zikrText,
    );

    await _databaseService.saveTasbihCounter(name, updatedCounter);
    await _loadTasbihCounters();
  }

  Future<void> resetTasbihCounter(String name) async {
    final counter = _tasbihCounters.firstWhere((c) => c.name == name);

    final resetCounter = TasbihCounter(
      name: counter.name,
      count: 0,
      target: counter.target,
      createdAt: counter.createdAt,
      lastUpdated: DateTime.now(),
      zikrText: counter.zikrText,
    );

    await _databaseService.saveTasbihCounter(name, resetCounter);
    await _loadTasbihCounters();
  }

  Future<void> deleteTasbihCounter(String name) async {
    await _databaseService.deleteTasbihCounter(name);
    await _loadTasbihCounters();
  }

  // Statistics methods
  int getTotalCompletedAzkar() {
    return _progressMap.values.where((progress) => progress.isCompleted).length;
  }

  int getTotalAzkarCount() {
    return _categories.fold(0, (total, category) => total + category.azkar.length);
  }

  double getOverallProgress() {
    final totalAzkar = getTotalAzkarCount();
    if (totalAzkar == 0) return 0.0;

    final completedAzkar = getTotalCompletedAzkar();
    return completedAzkar / totalAzkar;
  }

  Map<String, int> getDailyStats() {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);

    int todayCompleted = 0;
    int todayTotal = 0;

    for (final progress in _progressMap.values) {
      if (progress.lastUpdated.isAfter(todayStart)) {
        todayTotal++;
        if (progress.isCompleted) {
          todayCompleted++;
        }
      }
    }

    return {
      'completed': todayCompleted,
      'total': todayTotal,
    };
  }

  // Search methods
  List<Zikr> searchAzkar(String query) {
    if (query.isEmpty) return [];

    final results = <Zikr>[];
    for (final category in _categories) {
      for (final zikr in category.azkar) {
        if (zikr.text.contains(query) ||
            zikr.translation.contains(query) ||
            (zikr.reference?.contains(query) ?? false)) {
          results.add(zikr);
        }
      }
    }
    return results;
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
