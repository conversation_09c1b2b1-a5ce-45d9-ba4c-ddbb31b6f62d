import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/quran_provider.dart';
import '../../providers/settings_provider.dart';
import '../../providers/audio_provider.dart';
import '../../widgets/enhanced_surah_list_item.dart';
import '../../utils/rtl_helper.dart';

class EnhancedQuranScreen extends StatefulWidget {
  const EnhancedQuranScreen({super.key});

  @override
  State<EnhancedQuranScreen> createState() => _EnhancedQuranScreenState();
}

class _EnhancedQuranScreenState extends State<EnhancedQuranScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _searchAnimationController;
  late Animation<double> _searchAnimation;

  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  bool _isSearchVisible = false;
  String _searchQuery = '';
  int _selectedFilter = 0; // 0: الكل، 1: مكية، 2: مدنية

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchAnimationController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<QuranProvider, SettingsProvider, AudioProvider>(
      builder: (context, quranProvider, settings, audioProvider, child) {
        return Scaffold(
          body: RTLHelper.buildResponsiveRTLLayout(
            context: context,
            child: NestedScrollView(
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  _buildSliverAppBar(context, settings),
                  _buildSearchBar(context, settings),
                  _buildFilterChips(context, settings),
                  _buildTabBar(context, settings),
                ];
              },
              body: _buildTabBarView(context, quranProvider, settings),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSliverAppBar(BuildContext context, SettingsProvider settings) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: RTLHelper.buildDirectionalText(
          'القرآن الكريم',
          style: TextStyle(
            color: Colors.white,
            fontSize: settings.fontSize + 2,
            fontWeight: FontWeight.bold,
          ),
          forceRTL: true,
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                top: 20,
                left: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
              Positioned(
                bottom: -30,
                right: -30,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.05),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search, color: Colors.white),
          onPressed: _toggleSearch,
        ),
        IconButton(
          icon: const Icon(Icons.bookmark, color: Colors.white),
          onPressed: () => _showBookmarks(context),
        ),
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context, SettingsProvider settings) {
    return SliverToBoxAdapter(
      child: AnimatedBuilder(
        animation: _searchAnimation,
        builder: (context, child) {
          return SizeTransition(
            sizeFactor: _searchAnimation,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                textDirection: TextDirection.rtl,
                decoration: InputDecoration(
                  hintText: 'ابحث في السور...',
                  hintStyle: TextStyle(
                    fontSize: settings.fontSize,
                    color: Colors.grey[600],
                  ),
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: _clearSearch,
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFilterChips(BuildContext context, SettingsProvider settings) {
    return SliverToBoxAdapter(
      child: Container(
        height: 60,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: RTLHelper.buildRTLRow(
          children: [
            Expanded(
              child: ListView(
                scrollDirection: Axis.horizontal,
                reverse: true, // للاتجاه RTL
                children: [
                  _buildFilterChip('الكل', 0, settings),
                  const SizedBox(width: 8),
                  _buildFilterChip('مكية', 1, settings),
                  const SizedBox(width: 8),
                  _buildFilterChip('مدنية', 2, settings),
                  const SizedBox(width: 8),
                  _buildFilterChip('المفضلة', 3, settings),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, int index, SettingsProvider settings) {
    final isSelected = _selectedFilter == index;
    final theme = Theme.of(context);

    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : theme.colorScheme.primary,
          fontSize: settings.fontSize - 2,
          fontWeight: FontWeight.w500,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = selected ? index : 0;
        });
      },
      backgroundColor: theme.colorScheme.surface,
      selectedColor: theme.colorScheme.primary,
      checkmarkColor: Colors.white,
      elevation: isSelected ? 4 : 2,
      shadowColor: theme.colorScheme.primary.withValues(alpha: 0.3),
    );
  }

  Widget _buildTabBar(BuildContext context, SettingsProvider settings) {
    return SliverToBoxAdapter(
      child: Container(
        color: Theme.of(context).colorScheme.surface,
        child: TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).colorScheme.primary,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: Theme.of(context).colorScheme.primary,
          indicatorWeight: 3,
          labelStyle: TextStyle(
            fontSize: settings.fontSize - 2,
            fontWeight: FontWeight.bold,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: settings.fontSize - 2,
            fontWeight: FontWeight.normal,
          ),
          tabs: [
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.menu_book_rounded, size: 18),
                  const SizedBox(width: 4),
                  const Text('السور'),
                ],
              ),
            ),
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.library_books_rounded, size: 18),
                  const SizedBox(width: 4),
                  const Text('الأجزاء'),
                ],
              ),
            ),
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.article_rounded, size: 18),
                  const SizedBox(width: 4),
                  const Text('الصفحات'),
                ],
              ),
            ),
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.bookmark_rounded, size: 18),
                  const SizedBox(width: 4),
                  const Text('المفضلة'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBarView(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildSurahsList(context, quranProvider, settings),
        _buildJuzList(context, quranProvider, settings),
        _buildPagesList(context, quranProvider, settings),
        _buildBookmarksList(context, quranProvider, settings),
      ],
    );
  }

  Widget _buildSurahsList(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    if (quranProvider.isLoading) {
      return _buildLoadingState();
    }

    if (quranProvider.errorMessage != null) {
      return _buildErrorState(context, quranProvider);
    }

    final filteredSurahs = _getFilteredSurahs(quranProvider.surahs);

    if (filteredSurahs.isEmpty) {
      return _buildEmptyState('لا توجد سور تطابق البحث');
    }

    return RefreshIndicator(
      onRefresh: () => quranProvider.initialize(),
      child: ListView.builder(
        padding: const EdgeInsets.only(bottom: 100), // مساحة لشريط التشغيل
        itemCount: filteredSurahs.length,
        itemBuilder: (context, index) {
          final surah = filteredSurahs[index];
          final isLastRead = surah.number == quranProvider.lastReadSurah;

          return EnhancedSurahListItem(
            surah: surah,
            index: index,
            isLastRead: isLastRead,
            onDownload: () => _downloadSurah(context, surah, quranProvider),
          );
        },
      ),
    );
  }

  Widget _buildJuzList(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.library_books_rounded,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'قائمة الأجزاء',
            style: TextStyle(
              fontSize: settings.fontSize + 2,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قريباً...',
            style: TextStyle(
              fontSize: settings.fontSize,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPagesList(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.article_rounded,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'قائمة الصفحات',
            style: TextStyle(
              fontSize: settings.fontSize + 2,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قريباً...',
            style: TextStyle(
              fontSize: settings.fontSize,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookmarksList(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    if (quranProvider.bookmarks.isEmpty) {
      return _buildEmptyState('لا توجد مفضلة محفوظة');
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 100),
      itemCount: quranProvider.bookmarks.length,
      itemBuilder: (context, index) {
        final bookmark = quranProvider.bookmarks[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  '${bookmark.ayahNumber}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
            title: Text(bookmark.surahName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('الآية ${bookmark.ayahNumber}'),
                Text(
                  bookmark.ayahText.length > 50
                    ? '${bookmark.ayahText.substring(0, 50)}...'
                    : bookmark.ayahText,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            trailing: IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () async {
                await quranProvider.removeBookmark(
                  bookmark.surahNumber,
                  bookmark.ayahNumber,
                );
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم حذف العلامة المرجعية')),
                  );
                }
              },
            ),
            onTap: () {
              // Navigate to the bookmarked ayah
              Navigator.pushNamed(context, '/quran_bookmarks');
            },
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل القرآن الكريم...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, QuranProvider quranProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            quranProvider.errorMessage ?? 'حدث خطأ غير متوقع',
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => quranProvider.initialize(),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  List<dynamic> _getFilteredSurahs(List<dynamic> surahs) {
    var filtered = surahs;

    // تطبيق فلتر النوع
    if (_selectedFilter == 1) {
      filtered = filtered.where((s) => s.revelationType == 'Meccan').toList();
    } else if (_selectedFilter == 2) {
      filtered = filtered.where((s) => s.revelationType == 'Medinan').toList();
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((s) =>
        s.name.contains(_searchQuery) ||
        s.englishName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        s.englishNameTranslation.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
    }

    return filtered;
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
    });

    if (_isSearchVisible) {
      _searchAnimationController.forward();
      _searchFocusNode.requestFocus();
    } else {
      _searchAnimationController.reverse();
      _searchFocusNode.unfocus();
      _clearSearch();
    }
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  void _showBookmarks(BuildContext context) {
    _tabController.animateTo(3);
  }

  void _downloadSurah(BuildContext context, dynamic surah, QuranProvider quranProvider) async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('جاري تحميل سورة ${surah.name}...'),
          duration: const Duration(seconds: 2),
        ),
      );

      // Simulate download process
      await Future.delayed(const Duration(seconds: 2));

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحميل سورة ${surah.name} بنجاح'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل سورة ${surah.name}: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
