// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'azkar_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ZikrCategoryAdapter extends TypeAdapter<ZikrCategory> {
  @override
  final int typeId = 5;

  @override
  ZikrCategory read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Zikr<PERSON>ategory(
      id: fields[0] as int,
      name: fields[1] as String,
      description: fields[2] as String,
      icon: fields[3] as String,
      azkar: (fields[4] as List).cast<Zikr>(),
    );
  }

  @override
  void write(BinaryWriter writer, ZikrCategory obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.icon)
      ..writeByte(4)
      ..write(obj.azkar);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ZikrCategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ZikrAdapter extends TypeAdapter<Zikr> {
  @override
  final int typeId = 6;

  @override
  Zikr read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Zikr(
      id: fields[0] as int,
      text: fields[1] as String,
      translation: fields[2] as String,
      count: fields[3] as int,
      reference: fields[4] as String?,
      benefit: fields[5] as String?,
      audioUrl: fields[6] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Zikr obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.text)
      ..writeByte(2)
      ..write(obj.translation)
      ..writeByte(3)
      ..write(obj.count)
      ..writeByte(4)
      ..write(obj.reference)
      ..writeByte(5)
      ..write(obj.benefit)
      ..writeByte(6)
      ..write(obj.audioUrl);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ZikrAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ZikrProgressAdapter extends TypeAdapter<ZikrProgress> {
  @override
  final int typeId = 7;

  @override
  ZikrProgress read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ZikrProgress(
      zikrId: fields[0] as int,
      currentCount: fields[1] as int,
      totalCount: fields[2] as int,
      lastUpdated: fields[3] as DateTime,
      isCompleted: fields[4] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, ZikrProgress obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.zikrId)
      ..writeByte(1)
      ..write(obj.currentCount)
      ..writeByte(2)
      ..write(obj.totalCount)
      ..writeByte(3)
      ..write(obj.lastUpdated)
      ..writeByte(4)
      ..write(obj.isCompleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ZikrProgressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TasbihCounterAdapter extends TypeAdapter<TasbihCounter> {
  @override
  final int typeId = 8;

  @override
  TasbihCounter read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TasbihCounter(
      name: fields[0] as String,
      count: fields[1] as int,
      target: fields[2] as int,
      createdAt: fields[3] as DateTime,
      lastUpdated: fields[4] as DateTime,
      zikrText: fields[5] as String,
    );
  }

  @override
  void write(BinaryWriter writer, TasbihCounter obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.count)
      ..writeByte(2)
      ..write(obj.target)
      ..writeByte(3)
      ..write(obj.createdAt)
      ..writeByte(4)
      ..write(obj.lastUpdated)
      ..writeByte(5)
      ..write(obj.zikrText);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TasbihCounterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
