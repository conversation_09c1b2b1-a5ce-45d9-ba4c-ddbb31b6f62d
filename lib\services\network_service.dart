import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;

/// Network service for managing connectivity and API calls
class NetworkService {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  bool _isConnected = true;
  final StreamController<bool> _connectionController = StreamController<bool>.broadcast();

  /// Stream of connectivity changes
  Stream<bool> get connectionStream => _connectionController.stream;

  /// Current connection status
  bool get isConnected => _isConnected;

  /// Initialize network monitoring
  Future<void> initialize() async {
    // Check initial connectivity
    await _updateConnectionStatus();

    // Listen for connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) async {
        await _updateConnectionStatus();
      },
    );
  }

  /// Update connection status
  Future<void> _updateConnectionStatus() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();

      if (connectivityResults.contains(ConnectivityResult.none) || connectivityResults.isEmpty) {
        _isConnected = false;
      } else {
        // Even if we have connectivity, verify with actual internet access
        _isConnected = await _hasInternetAccess();
      }

      _connectionController.add(_isConnected);
    } catch (e) {
      _isConnected = false;
      _connectionController.add(_isConnected);
    }
  }

  /// Check if device has actual internet access
  Future<bool> _hasInternetAccess() async {
    try {
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Make HTTP GET request with network checking
  Future<http.Response?> get(
    String url, {
    Map<String, String>? headers,
    Duration timeout = const Duration(seconds: 10),
  }) async {
    if (!_isConnected) {
      throw NetworkException('لا يوجد اتصال بالإنترنت');
    }

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: headers ?? {'Accept': 'application/json'},
      ).timeout(timeout);

      return response;
    } on SocketException {
      throw NetworkException('فشل في الاتصال بالخادم');
    } on TimeoutException {
      throw NetworkException('انتهت مهلة الاتصال');
    } on HttpException catch (e) {
      throw NetworkException('خطأ في الشبكة: ${e.message}');
    } catch (e) {
      throw NetworkException('خطأ غير متوقع: $e');
    }
  }

  /// Make HTTP POST request with network checking
  Future<http.Response?> post(
    String url, {
    Map<String, String>? headers,
    dynamic body,
    Duration timeout = const Duration(seconds: 10),
  }) async {
    if (!_isConnected) {
      throw NetworkException('لا يوجد اتصال بالإنترنت');
    }

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: headers ?? {'Accept': 'application/json'},
        body: body,
      ).timeout(timeout);

      return response;
    } on SocketException {
      throw NetworkException('فشل في الاتصال بالخادم');
    } on TimeoutException {
      throw NetworkException('انتهت مهلة الاتصال');
    } on HttpException catch (e) {
      throw NetworkException('خطأ في الشبكة: ${e.message}');
    } catch (e) {
      throw NetworkException('خطأ غير متوقع: $e');
    }
  }

  /// Check if specific API endpoint is reachable
  Future<bool> isApiReachable(String baseUrl) async {
    try {
      final response = await http.head(Uri.parse(baseUrl))
          .timeout(const Duration(seconds: 5));
      return response.statusCode < 400;
    } catch (e) {
      return false;
    }
  }

  /// Get network type (WiFi, Mobile, etc.)
  Future<String> getNetworkType() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();

      if (connectivityResults.contains(ConnectivityResult.wifi)) {
        return 'WiFi';
      } else if (connectivityResults.contains(ConnectivityResult.mobile)) {
        return 'Mobile Data';
      } else if (connectivityResults.contains(ConnectivityResult.ethernet)) {
        return 'Ethernet';
      } else if (connectivityResults.contains(ConnectivityResult.bluetooth)) {
        return 'Bluetooth';
      } else if (connectivityResults.contains(ConnectivityResult.none) || connectivityResults.isEmpty) {
        return 'No Connection';
      } else {
        return 'Unknown';
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  /// Get network speed estimate (rough)
  Future<NetworkSpeed> getNetworkSpeed() async {
    if (!_isConnected) return NetworkSpeed.none;

    try {
      final stopwatch = Stopwatch()..start();

      // Download a small file to test speed
      await http.get(Uri.parse('https://httpbin.org/bytes/1024'))
          .timeout(const Duration(seconds: 5));

      stopwatch.stop();
      final milliseconds = stopwatch.elapsedMilliseconds;

      if (milliseconds < 500) return NetworkSpeed.fast;
      if (milliseconds < 1500) return NetworkSpeed.medium;
      return NetworkSpeed.slow;
    } catch (e) {
      return NetworkSpeed.unknown;
    }
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectionController.close();
  }
}

/// Network speed enumeration
enum NetworkSpeed {
  none,
  slow,
  medium,
  fast,
  unknown,
}

/// Network exception class
class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);

  @override
  String toString() => 'NetworkException: $message';
}

/// Network utility functions
class NetworkUtils {
  /// Check if URL is valid
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Get domain from URL
  static String? getDomain(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return null;
    }
  }

  /// Check if device is on metered connection
  static Future<bool> isMeteredConnection() async {
    try {
      final connectivity = Connectivity();
      final results = await connectivity.checkConnectivity();
      return results.contains(ConnectivityResult.mobile);
    } catch (e) {
      return false;
    }
  }

  /// Format bytes to human readable
  static String formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
