import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/azkar_models.dart';
import '../utils/error_handler.dart';

class AzkarApiService {
  // Using Hisn Elmuslim API (open source)
  static const String _hisnElmuslimApi = 'https://raw.githubusercontent.com/mohamednagy/Hisn-Elmuslim-API/master/azkar.json';

  // Cache for azkar data
  static List<ZikrCategory>? _cachedAzkar;

  /// Get all azkar categories with improved error handling
  Future<List<ZikrCategory>> getAllAzkarCategories() async {
    if (_cachedAzkar != null) {
      return _cachedAzkar!;
    }

    try {
      // Try to get from Hisn Elmuslim API first with timeout
      final response = await http.get(
        Uri.parse(_hisnElmuslimApi),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final parsedData = _parseHisnElmuslimData(data);

        // Validate that we got meaningful data
        if (parsedData.isNotEmpty && parsedData.any((cat) => cat.azkar.isNotEmpty)) {
          _cachedAzkar = parsedData;
          return _cachedAzkar!;
        }
      }
    } catch (e, stackTrace) {
      ErrorHandler.logError(e, stackTrace as StackTrace?);
    }

    // Try alternative API sources
    try {
      final alternativeData = await _getAlternativeAzkarData();
      if (alternativeData.isNotEmpty) {
        _cachedAzkar = alternativeData;
        return _cachedAzkar!;
      }
    } catch (e, stackTrace) {
      ErrorHandler.logError(e, stackTrace as StackTrace?);
    }

    // Fallback to default azkar
    _cachedAzkar = AzkarData.getDefaultCategories();
    return _cachedAzkar!;
  }

  /// Try to get azkar from alternative sources
  Future<List<ZikrCategory>> _getAlternativeAzkarData() async {
    // This could be expanded to include other APIs or local JSON files
    // For now, return empty to fall back to default data
    return [];
  }

  /// Parse Hisn Elmuslim API data
  List<ZikrCategory> _parseHisnElmuslimData(Map<String, dynamic> data) {
    List<ZikrCategory> categories = [];

    // Parse different categories from the API
    if (data.containsKey('أذكار الصباح')) {
      categories.add(_createCategory(
        1, 'أذكار الصباح', 'أذكار تُقال في الصباح', 'morning',
        data['أذكار الصباح'] as List,
      ));
    }

    if (data.containsKey('أذكار المساء')) {
      categories.add(_createCategory(
        2, 'أذكار المساء', 'أذكار تُقال في المساء', 'evening',
        data['أذكار المساء'] as List,
      ));
    }

    if (data.containsKey('أذكار النوم')) {
      categories.add(_createCategory(
        3, 'أذكار النوم', 'أذكار تُقال قبل النوم', 'sleep',
        data['أذكار النوم'] as List,
      ));
    }

    if (data.containsKey('أذكار الاستيقاظ')) {
      categories.add(_createCategory(
        4, 'أذكار الاستيقاظ', 'أذكار تُقال عند الاستيقاظ', 'wakeup',
        data['أذكار الاستيقاظ'] as List,
      ));
    }

    if (data.containsKey('أذكار الصلاة')) {
      categories.add(_createCategory(
        5, 'أذكار الصلاة', 'أذكار تُقال بعد الصلاة', 'prayer',
        data['أذكار الصلاة'] as List,
      ));
    }

    return categories;
  }

  /// Create category from API data
  ZikrCategory _createCategory(int id, String name, String description, String icon, List apiData) {
    List<Zikr> azkar = [];

    for (int i = 0; i < apiData.length; i++) {
      final item = apiData[i];
      if (item is Map<String, dynamic>) {
        azkar.add(Zikr(
          id: i + 1,
          text: item['text'] ?? item['arabic'] ?? '',
          translation: item['translation'] ?? '',
          count: item['count'] ?? item['repetitions'] ?? 1,
          reference: item['reference'] ?? item['source'],
          benefit: item['benefits'],
        ));
      } else if (item is String) {
        azkar.add(Zikr(
          id: i + 1,
          text: item,
          translation: '',
          count: 1,
          reference: null,
          benefit: null,
        ));
      }
    }

    return ZikrCategory(
      id: id,
      name: name,
      description: description,
      icon: icon,
      azkar: azkar,
    );
  }

  /// Get azkar by category
  Future<List<Zikr>> getAzkarByCategory(String categoryName) async {
    final categories = await getAllAzkarCategories();

    for (var category in categories) {
      if (category.name.contains(categoryName)) {
        return category.azkar;
      }
    }

    return [];
  }

  /// Get morning azkar
  Future<List<Zikr>> getMorningAzkar() async {
    return await getAzkarByCategory('الصباح');
  }

  /// Get evening azkar
  Future<List<Zikr>> getEveningAzkar() async {
    return await getAzkarByCategory('المساء');
  }

  /// Get sleep azkar
  Future<List<Zikr>> getSleepAzkar() async {
    return await getAzkarByCategory('النوم');
  }

  /// Get prayer azkar
  Future<List<Zikr>> getPrayerAzkar() async {
    return await getAzkarByCategory('الصلاة');
  }

  /// Search azkar
  Future<List<Zikr>> searchAzkar(String query) async {
    final categories = await getAllAzkarCategories();
    List<Zikr> results = [];

    for (var category in categories) {
      for (var zikr in category.azkar) {
        if (zikr.text.contains(query) ||
            zikr.translation.contains(query) ||
            (zikr.benefit?.contains(query) ?? false)) {
          results.add(zikr);
        }
      }
    }

    return results;
  }

  /// Get random zikr
  Future<Zikr?> getRandomZikr() async {
    final categories = await getAllAzkarCategories();

    if (categories.isEmpty) return null;

    final allZikrs = <Zikr>[];
    for (var category in categories) {
      allZikrs.addAll(category.azkar);
    }

    if (allZikrs.isEmpty) return null;

    final random = DateTime.now().millisecondsSinceEpoch % allZikrs.length;
    return allZikrs[random];
  }

  /// Clear cache
  void clearCache() {
    _cachedAzkar = null;
  }

  /// Get azkar statistics
  Future<Map<String, int>> getAzkarStatistics() async {
    final categories = await getAllAzkarCategories();

    int totalZikrs = 0;
    Map<String, int> categoryCount = {};

    for (var category in categories) {
      totalZikrs += category.azkar.length;
      categoryCount[category.name] = category.azkar.length;
    }

    return {
      'total': totalZikrs,
      'categories': categories.length,
      ...categoryCount,
    };
  }

  /// Get zikr by ID
  Future<Zikr?> getZikrById(int zikrId) async {
    final categories = await getAllAzkarCategories();

    for (var category in categories) {
      for (var zikr in category.azkar) {
        if (zikr.id == zikrId) {
          return zikr;
        }
      }
    }

    return null;
  }

  /// Get category by name
  Future<ZikrCategory?> getCategoryByName(String categoryName) async {
    final categories = await getAllAzkarCategories();

    for (var category in categories) {
      if (category.name == categoryName) {
        return category;
      }
    }

    return null;
  }

  /// Get enhanced azkar with additional sources
  Future<List<ZikrCategory>> getEnhancedAzkar() async {
    final defaultCategories = AzkarData.getDefaultCategories();

    // Try to enhance with API data
    try {
      final apiCategories = await getAllAzkarCategories();
      if (apiCategories.isNotEmpty) {
        return apiCategories;
      }
    } catch (e, stackTrace) {
      ErrorHandler.logError(e, stackTrace as StackTrace?);
    }

    return defaultCategories;
  }

  /// Get azkar for specific time
  Future<List<Zikr>> getAzkarForTime(String timeOfDay) async {
    switch (timeOfDay.toLowerCase()) {
      case 'morning':
      case 'صباح':
        return await getMorningAzkar();
      case 'evening':
      case 'مساء':
        return await getEveningAzkar();
      case 'sleep':
      case 'نوم':
        return await getSleepAzkar();
      case 'prayer':
      case 'صلاة':
        return await getPrayerAzkar();
      default:
        return [];
    }
  }

  /// Get popular azkar (most commonly recited)
  Future<List<Zikr>> getPopularAzkar() async {
    final categories = await getAllAzkarCategories();
    List<Zikr> popularAzkar = [];

    // Get some popular azkar from different categories
    for (var category in categories) {
      if (category.azkar.isNotEmpty) {
        // Add first few azkar from each category as they're usually the most important
        popularAzkar.addAll(category.azkar.take(2));
      }
    }

    return popularAzkar;
  }

  /// Check if azkar data is cached
  bool get isCached => _cachedAzkar != null;

  /// Get cache timestamp
  DateTime? get cacheTimestamp => _cachedAzkar != null ? DateTime.now() : null;
}
