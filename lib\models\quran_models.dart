import 'package:hive/hive.dart';

part 'quran_models.g.dart';

@HiveType(typeId: 0)
class <PERSON><PERSON> extends HiveObject {
  @HiveField(0)
  final int number;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String englishName;
  
  @HiveField(3)
  final String englishNameTranslation;
  
  @HiveField(4)
  final String revelationType;
  
  @HiveField(5)
  final int numberOfAyahs;
  
  @HiveField(6)
  final List<Ayah> ayahs;

  Surah({
    required this.number,
    required this.name,
    required this.englishName,
    required this.englishNameTranslation,
    required this.revelationType,
    required this.numberOfAyahs,
    required this.ayahs,
  });

  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      number: json['number'],
      name: json['name'],
      englishName: json['englishName'],
      englishNameTranslation: json['englishNameTranslation'],
      revelationType: json['revelationType'],
      numberOfAyahs: json['numberOfAyahs'],
      ayahs: (json['ayahs'] as List)
          .map((ayah) => Ayah.fromJson(ayah))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'englishName': englishName,
      'englishNameTranslation': englishNameTranslation,
      'revelationType': revelationType,
      'numberOfAyahs': numberOfAyahs,
      'ayahs': ayahs.map((ayah) => ayah.toJson()).toList(),
    };
  }
}

@HiveType(typeId: 1)
class Ayah extends HiveObject {
  @HiveField(0)
  final int number;
  
  @HiveField(1)
  final String text;
  
  @HiveField(2)
  final int numberInSurah;
  
  @HiveField(3)
  final int juz;
  
  @HiveField(4)
  final int manzil;
  
  @HiveField(5)
  final int page;
  
  @HiveField(6)
  final int ruku;
  
  @HiveField(7)
  final int hizbQuarter;
  
  @HiveField(8)
  final bool sajda;

  Ayah({
    required this.number,
    required this.text,
    required this.numberInSurah,
    required this.juz,
    required this.manzil,
    required this.page,
    required this.ruku,
    required this.hizbQuarter,
    required this.sajda,
  });

  factory Ayah.fromJson(Map<String, dynamic> json) {
    return Ayah(
      number: json['number'],
      text: json['text'],
      numberInSurah: json['numberInSurah'],
      juz: json['juz'],
      manzil: json['manzil'],
      page: json['page'],
      ruku: json['ruku'],
      hizbQuarter: json['hizbQuarter'],
      sajda: json['sajda'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text': text,
      'numberInSurah': numberInSurah,
      'juz': juz,
      'manzil': manzil,
      'page': page,
      'ruku': ruku,
      'hizbQuarter': hizbQuarter,
      'sajda': sajda,
    };
  }
}

@HiveType(typeId: 2)
class Tafsir extends HiveObject {
  @HiveField(0)
  final int ayahNumber;
  
  @HiveField(1)
  final String text;
  
  @HiveField(2)
  final String author;

  Tafsir({
    required this.ayahNumber,
    required this.text,
    required this.author,
  });

  factory Tafsir.fromJson(Map<String, dynamic> json) {
    return Tafsir(
      ayahNumber: json['ayahNumber'],
      text: json['text'],
      author: json['author'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ayahNumber': ayahNumber,
      'text': text,
      'author': author,
    };
  }
}

@HiveType(typeId: 3)
class Reciter extends HiveObject {
  @HiveField(0)
  final int id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String arabicName;
  
  @HiveField(3)
  final String style;
  
  @HiveField(4)
  final String baseUrl;

  Reciter({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.style,
    required this.baseUrl,
  });

  factory Reciter.fromJson(Map<String, dynamic> json) {
    return Reciter(
      id: json['id'],
      name: json['name'],
      arabicName: json['arabicName'],
      style: json['style'],
      baseUrl: json['baseUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'arabicName': arabicName,
      'style': style,
      'baseUrl': baseUrl,
    };
  }
}

@HiveType(typeId: 4)
class BookmarkModel extends HiveObject {
  @HiveField(0)
  final int surahNumber;
  
  @HiveField(1)
  final int ayahNumber;
  
  @HiveField(2)
  final String surahName;
  
  @HiveField(3)
  final String ayahText;
  
  @HiveField(4)
  final DateTime createdAt;
  
  @HiveField(5)
  final String? note;

  BookmarkModel({
    required this.surahNumber,
    required this.ayahNumber,
    required this.surahName,
    required this.ayahText,
    required this.createdAt,
    this.note,
  });

  Map<String, dynamic> toJson() {
    return {
      'surahNumber': surahNumber,
      'ayahNumber': ayahNumber,
      'surahName': surahName,
      'ayahText': ayahText,
      'createdAt': createdAt.toIso8601String(),
      'note': note,
    };
  }
}
