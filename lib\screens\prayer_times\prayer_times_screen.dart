import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../services/prayer_times_service.dart';

class PrayerTimesScreen extends StatefulWidget {
  const PrayerTimesScreen({super.key});

  @override
  State<PrayerTimesScreen> createState() => _PrayerTimesScreenState();
}

class _PrayerTimesScreenState extends State<PrayerTimesScreen> {
  final PrayerTimesService _prayerTimesService = PrayerTimesService();

  Map<String, TimeOfDay>? _prayerTimes;
  Map<String, dynamic>? _islamicDate;
  String? _nextPrayer;
  Duration? _timeUntilNext;
  bool _isLoading = true;
  String? _errorMessage;

  // Default location (Makkah)
  final double _latitude = 21.3891;
  final double _longitude = 39.8579;
  String _cityName = 'مكة المكرمة';

  @override
  void initState() {
    super.initState();
    _loadPrayerTimes();
    _startTimer();
  }

  void _startTimer() {
    // Update every minute
    Future.delayed(const Duration(minutes: 1), () {
      if (mounted) {
        _updateTimeUntilNext();
        _startTimer();
      }
    });
  }

  void _updateTimeUntilNext() {
    if (_prayerTimes != null) {
      setState(() {
        _nextPrayer = _prayerTimesService.getNextPrayer(_prayerTimes!);
        _timeUntilNext = _prayerTimesService.getTimeUntilNextPrayer(_prayerTimes!);
      });
    }
  }

  Future<void> _loadPrayerTimes() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Try to get location permission and current location
      await _getCurrentLocation();

      // Get prayer times
      final prayerData = await _prayerTimesService.getPrayerTimes(
        latitude: _latitude,
        longitude: _longitude,
      );

      if (prayerData != null) {
        setState(() {
          _prayerTimes = _prayerTimesService.parsePrayerTimesArabic(prayerData);
          _nextPrayer = _prayerTimesService.getNextPrayer(_prayerTimes!);
          _timeUntilNext = _prayerTimesService.getTimeUntilNextPrayer(_prayerTimes!);
        });
      }

      // Get Islamic date
      final islamicDate = await _prayerTimesService.getIslamicDate();
      if (islamicDate != null) {
        setState(() {
          _islamicDate = islamicDate;
        });
      }

    } catch (e) {
      setState(() {
        _errorMessage = 'فشل في تحميل أوقات الصلاة: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final status = await Permission.location.request();
      if (status.isGranted) {
        // Here you would typically use a location package like geolocator
        // For now, we'll use default location (Makkah)
        setState(() {
          _cityName = 'مكة المكرمة';
        });
      }
    } catch (e) {
      // Use default location if permission denied
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF00BCD4), Color(0xFF26C6DA)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.mosque,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'أوقات الصلاة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF00BCD4), Color(0xFF26C6DA)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.refresh_rounded, color: Colors.white),
              onPressed: _loadPrayerTimes,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.location_on_rounded, color: Colors.white),
              onPressed: _showLocationDialog,
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorWidget()
              : _buildPrayerTimesContent(),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadPrayerTimes,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildPrayerTimesContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildLocationCard(),
          const SizedBox(height: 16),
          _buildNextPrayerCard(),
          const SizedBox(height: 16),
          _buildIslamicDateCard(),
          const SizedBox(height: 16),
          _buildPrayerTimesCard(),
        ],
      ),
    );
  }

  Widget _buildLocationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.location_on, color: Colors.green),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الموقع الحالي',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    _cityName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNextPrayerCard() {
    if (_nextPrayer == null || _timeUntilNext == null) {
      return const SizedBox.shrink();
    }

    final hours = _timeUntilNext!.inHours;
    final minutes = _timeUntilNext!.inMinutes % 60;

    return Card(
      color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Text(
              'الصلاة القادمة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _nextPrayer!,
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'بعد ${hours > 0 ? '$hours ساعة و' : ''}$minutes دقيقة',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIslamicDateCard() {
    if (_islamicDate == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, color: Colors.blue),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'التاريخ الهجري',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    '${_islamicDate!['day']} ${_islamicDate!['month']['ar']} ${_islamicDate!['year']} هـ',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerTimesCard() {
    if (_prayerTimes == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أوقات الصلاة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ..._prayerTimes!.entries.map((entry) {
              final isNext = entry.key == _nextPrayer;
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isNext
                      ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: isNext
                      ? Border.all(color: Theme.of(context).primaryColor)
                      : null,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      entry.key,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: isNext ? FontWeight.bold : FontWeight.normal,
                        color: isNext ? Theme.of(context).primaryColor : null,
                      ),
                    ),
                    Text(
                      _prayerTimesService.formatTime(entry.value),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: isNext ? FontWeight.bold : FontWeight.normal,
                        color: isNext ? Theme.of(context).primaryColor : null,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  void _showLocationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير الموقع'),
        content: const Text('سيتم إضافة إمكانية تغيير الموقع قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
