import 'package:hive_flutter/hive_flutter.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import '../models/quran_models.dart';
import '../models/azkar_models.dart';
import '../models/hadith_models.dart';

class DatabaseService {
  static DatabaseService? _instance;
  static DatabaseService get instance => _instance ??= DatabaseService._();
  DatabaseService._();

  Database? _database;
  
  // Hive boxes
  Box<Surah>? _surahBox;
  Box<BookmarkModel>? _bookmarkBox;
  Box<ZikrProgress>? _zikrProgressBox;
  Box<TasbihCounter>? _tasbihBox;
  Box<HadithOfTheDay>? _hadithOfTheDayBox;

  Future<void> initializeDatabase() async {
    try {
      await _initializeHive();
      await _initializeSQLite();
    } catch (e) {
      print('Database initialization failed: $e');
      // Continue with limited functionality
    }
  }

  Future<void> _initializeHive() async {
    await Hive.initFlutter();
    
    // Register adapters
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(SurahAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(AyahAdapter());
    }
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(TafsirAdapter());
    }
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(ReciterAdapter());
    }
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(BookmarkModelAdapter());
    }
    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(ZikrCategoryAdapter());
    }
    if (!Hive.isAdapterRegistered(6)) {
      Hive.registerAdapter(ZikrAdapter());
    }
    if (!Hive.isAdapterRegistered(7)) {
      Hive.registerAdapter(ZikrProgressAdapter());
    }
    if (!Hive.isAdapterRegistered(8)) {
      Hive.registerAdapter(TasbihCounterAdapter());
    }
    if (!Hive.isAdapterRegistered(9)) {
      Hive.registerAdapter(HadithCategoryAdapter());
    }
    if (!Hive.isAdapterRegistered(10)) {
      Hive.registerAdapter(HadithAdapter());
    }
    if (!Hive.isAdapterRegistered(11)) {
      Hive.registerAdapter(HadithOfTheDayAdapter());
    }

    // Open boxes
    _surahBox = await Hive.openBox<Surah>('surahs');
    _bookmarkBox = await Hive.openBox<BookmarkModel>('bookmarks');
    _zikrProgressBox = await Hive.openBox<ZikrProgress>('zikr_progress');
    _tasbihBox = await Hive.openBox<TasbihCounter>('tasbih_counters');
    _hadithOfTheDayBox = await Hive.openBox<HadithOfTheDay>('hadith_of_the_day');
  }

  Future<void> _initializeSQLite() async {
    final databasePath = await getDatabasesPath();
    final path = join(databasePath, 'quraan.db');

    _database = await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // Create tables for complex queries and relationships
    await db.execute('''
      CREATE TABLE reading_progress (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        surah_number INTEGER NOT NULL,
        ayah_number INTEGER NOT NULL,
        timestamp INTEGER NOT NULL,
        reading_time INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE user_preferences (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE notification_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        enabled INTEGER NOT NULL DEFAULT 1,
        time TEXT,
        days TEXT
      )
    ''');
  }

  // Quran related methods
  Future<void> saveSurah(Surah surah) async {
    await _surahBox?.put(surah.number, surah);
  }

  Future<Surah?> getSurah(int number) async {
    return _surahBox?.get(number);
  }

  Future<List<Surah>> getAllSurahs() async {
    return _surahBox?.values.toList() ?? [];
  }

  // Bookmark methods
  Future<void> addBookmark(BookmarkModel bookmark) async {
    final key = '${bookmark.surahNumber}_${bookmark.ayahNumber}';
    await _bookmarkBox?.put(key, bookmark);
  }

  Future<void> removeBookmark(int surahNumber, int ayahNumber) async {
    final key = '${surahNumber}_$ayahNumber';
    await _bookmarkBox?.delete(key);
  }

  Future<List<BookmarkModel>> getAllBookmarks() async {
    return _bookmarkBox?.values.toList() ?? [];
  }

  Future<bool> isBookmarked(int surahNumber, int ayahNumber) async {
    final key = '${surahNumber}_$ayahNumber';
    return _bookmarkBox?.containsKey(key) ?? false;
  }

  // Zikr progress methods
  Future<void> saveZikrProgress(ZikrProgress progress) async {
    await _zikrProgressBox?.put(progress.zikrId, progress);
  }

  Future<ZikrProgress?> getZikrProgress(int zikrId) async {
    return _zikrProgressBox?.get(zikrId);
  }

  Future<void> resetZikrProgress(int zikrId) async {
    await _zikrProgressBox?.delete(zikrId);
  }

  // Tasbih methods
  Future<void> saveTasbihCounter(String name, TasbihCounter counter) async {
    await _tasbihBox?.put(name, counter);
  }

  Future<TasbihCounter?> getTasbihCounter(String name) async {
    return _tasbihBox?.get(name);
  }

  Future<List<TasbihCounter>> getAllTasbihCounters() async {
    return _tasbihBox?.values.toList() ?? [];
  }

  Future<void> deleteTasbihCounter(String name) async {
    await _tasbihBox?.delete(name);
  }

  // Hadith of the day methods
  Future<void> saveHadithOfTheDay(HadithOfTheDay hadithOfTheDay) async {
    final key = hadithOfTheDay.date.toIso8601String().split('T')[0];
    await _hadithOfTheDayBox?.put(key, hadithOfTheDay);
  }

  Future<HadithOfTheDay?> getHadithOfTheDay(DateTime date) async {
    final key = date.toIso8601String().split('T')[0];
    return _hadithOfTheDayBox?.get(key);
  }

  // Reading progress methods (SQLite)
  Future<void> saveReadingProgress(int surahNumber, int ayahNumber, int readingTime) async {
    await _database?.insert(
      'reading_progress',
      {
        'surah_number': surahNumber,
        'ayah_number': ayahNumber,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'reading_time': readingTime,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Map<String, dynamic>>> getReadingProgress() async {
    return await _database?.query('reading_progress', orderBy: 'timestamp DESC') ?? [];
  }

  // User preferences methods (SQLite)
  Future<void> savePreference(String key, String value) async {
    await _database?.insert(
      'user_preferences',
      {'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getPreference(String key) async {
    final result = await _database?.query(
      'user_preferences',
      where: 'key = ?',
      whereArgs: [key],
    );
    
    if (result?.isNotEmpty == true) {
      return result!.first['value'] as String;
    }
    return null;
  }

  // Notification settings methods (SQLite)
  Future<void> saveNotificationSetting(String type, bool enabled, {String? time, String? days}) async {
    await _database?.insert(
      'notification_settings',
      {
        'type': type,
        'enabled': enabled ? 1 : 0,
        'time': time,
        'days': days,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Map<String, dynamic>>> getNotificationSettings() async {
    return await _database?.query('notification_settings') ?? [];
  }

  // Clear all data
  Future<void> clearAllData() async {
    await _surahBox?.clear();
    await _bookmarkBox?.clear();
    await _zikrProgressBox?.clear();
    await _tasbihBox?.clear();
    await _hadithOfTheDayBox?.clear();
    
    await _database?.delete('reading_progress');
    await _database?.delete('user_preferences');
    await _database?.delete('notification_settings');
  }

  // Close database
  Future<void> close() async {
    await _surahBox?.close();
    await _bookmarkBox?.close();
    await _zikrProgressBox?.close();
    await _tasbihBox?.close();
    await _hadithOfTheDayBox?.close();
    await _database?.close();
  }
}
