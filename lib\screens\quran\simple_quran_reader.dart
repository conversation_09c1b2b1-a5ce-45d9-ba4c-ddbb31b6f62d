import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/quran_provider.dart';
import '../../providers/settings_provider.dart';
import '../../models/quran_models.dart';

class SimpleQuranReader extends StatefulWidget {
  const SimpleQuranReader({super.key});

  @override
  State<SimpleQuranReader> createState() => _SimpleQuranReaderState();
}

class _SimpleQuranReaderState extends State<SimpleQuranReader> {
  final PageController _pageController = PageController();
  int _currentPage = 1;
  bool _isLoading = true;
  List<Surah> _surahs = [];

  @override
  void initState() {
    super.initState();
    // تأخير تحميل البيانات لتجنب خطأ setState أثناء البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadQuranData();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadQuranData() async {
    try {
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);
      
      // التحقق من وجود البيانات أولاً
      if (quranProvider.surahs.isNotEmpty) {
        setState(() {
          _surahs = quranProvider.surahs;
          _isLoading = false;
        });
        return;
      }
      
      // تحميل البيانات إذا لم تكن موجودة
      await quranProvider.initialize();
      
      if (mounted) {
        setState(() {
          _surahs = quranProvider.surahs;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading Quran data: $e');
      if (mounted) {
        setState(() {
          _surahs = _getDefaultSurahs(); // استخدام بيانات افتراضية
          _isLoading = false;
        });
      }
    }
  }

  // بيانات افتراضية في حالة فشل التحميل
  List<Surah> _getDefaultSurahs() {
    return [
      Surah(number: 1, name: 'الفاتحة', englishName: 'Al-Fatiha', englishNameTranslation: 'The Opening', revelationType: 'مكية', numberOfAyahs: 7, ayahs: [
        Ayah(number: 1, text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ', numberInSurah: 1, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 2, text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ', numberInSurah: 2, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 3, text: 'الرَّحْمَٰنِ الرَّحِيمِ', numberInSurah: 3, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 4, text: 'مَالِكِ يَوْمِ الدِّينِ', numberInSurah: 4, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 5, text: 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ', numberInSurah: 5, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 6, text: 'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ', numberInSurah: 6, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 7, text: 'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ', numberInSurah: 7, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
      ]),
      // يمكن إضافة المزيد من السور الافتراضية هنا
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المصحف الشريف'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.list_rounded),
            onPressed: _showSurahsList,
            tooltip: 'فهرس السور',
          ),
          IconButton(
            icon: const Icon(Icons.text_fields_rounded),
            onPressed: _showFontSettings,
            tooltip: 'إعدادات الخط',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'جاري تحميل المصحف الشريف...',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            )
          : _surahs.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'لم يتم العثور على بيانات القرآن',
                        style: TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    // شريط التنقل
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.arrow_back_ios_rounded),
                            onPressed: _currentPage > 1 ? _previousPage : null,
                            color: _currentPage > 1 
                              ? const Color(0xFF4CAF50) 
                              : Colors.grey,
                          ),
                          Text(
                            'صفحة $_currentPage من ${_getTotalPages()}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.arrow_forward_ios_rounded),
                            onPressed: _currentPage < _getTotalPages() ? _nextPage : null,
                            color: _currentPage < _getTotalPages() 
                              ? const Color(0xFF4CAF50) 
                              : Colors.grey,
                          ),
                        ],
                      ),
                    ),
                    
                    // محتوى المصحف
                    Expanded(
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: (page) {
                          setState(() {
                            _currentPage = page + 1;
                          });
                        },
                        itemCount: _getTotalPages(),
                        itemBuilder: (context, pageIndex) {
                          return _buildQuranPage(pageIndex);
                        },
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildQuranPage(int pageIndex) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        final surahsForPage = _getSurahsForPage(pageIndex);
        
        if (surahsForPage.isEmpty) {
          return const Center(
            child: Text(
              'لا توجد سور في هذه الصفحة',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          );
        }
        
        return Container(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Column(
              children: [
                // عرض السور في هذه الصفحة
                ...surahsForPage.map((surah) => _buildSurahWidget(surah, settings)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSurahWidget(Surah surah, SettingsProvider settings) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // رأس السورة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  'سورة ${surah.name}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${surah.revelationType} • ${surah.numberOfAyahs} آية',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // البسملة (إلا للتوبة)
          if (surah.number != 9)
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: settings.fontSize + 2,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF4CAF50),
                  height: 2.0,
                ),
              ),
            ),
          
          // آيات السورة
          ...surah.ayahs.map((ayah) => _buildAyahWidget(ayah, settings)),
        ],
      ),
    );
  }

  Widget _buildAyahWidget(Ayah ayah, SettingsProvider settings) {
    // ألوان متدرجة للآيات
    final colors = [
      Colors.blue.shade50,
      Colors.green.shade50,
      Colors.purple.shade50,
      Colors.orange.shade50,
      Colors.teal.shade50,
      Colors.pink.shade50,
      Colors.indigo.shade50,
    ];
    
    final colorIndex = (ayah.numberInSurah - 1) % colors.length;
    final backgroundColor = colors[colorIndex];
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300, width: 0.5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // نص الآية
          Text(
            ayah.text,
            textAlign: TextAlign.justify,
            style: TextStyle(
              fontSize: settings.fontSize,
              height: 2.2,
              color: Colors.black87,
              fontWeight: FontWeight.w400,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // رقم الآية
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  '${ayah.numberInSurah}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Surah> _getSurahsForPage(int pageIndex) {
    // توزيع السور على الصفحات (مبسط)
    const surahsPerPage = 2;
    final startIndex = pageIndex * surahsPerPage;
    final endIndex = (startIndex + surahsPerPage).clamp(0, _surahs.length);
    
    if (startIndex >= _surahs.length) return [];
    
    return _surahs.sublist(startIndex, endIndex);
  }

  int _getTotalPages() {
    const surahsPerPage = 2;
    return (_surahs.length / surahsPerPage).ceil();
  }

  void _nextPage() {
    if (_currentPage < _getTotalPages()) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showSurahsList() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // العنوان
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'فهرس السور',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            
            // قائمة السور
            Expanded(
              child: ListView.builder(
                itemCount: _surahs.length,
                itemBuilder: (context, index) {
                  final surah = _surahs[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: const Color(0xFF4CAF50),
                      child: Text(
                        '${surah.number}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(surah.name),
                    subtitle: Text('${surah.numberOfAyahs} آية • ${surah.revelationType}'),
                    onTap: () {
                      Navigator.pop(context);
                      _goToSurah(surah);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _goToSurah(Surah surah) {
    // العثور على الصفحة التي تحتوي على السورة
    const surahsPerPage = 2;
    final pageIndex = (surah.number - 1) ~/ surahsPerPage;
    
    _pageController.animateToPage(
      pageIndex,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  void _showFontSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Consumer<SettingsProvider>(
          builder: (context, settings, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // مقبض السحب
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                
                const Text(
                  'إعدادات الخط',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // حجم الخط
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('حجم الخط'),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.remove),
                          onPressed: () => settings.setFontSize(settings.fontSize - 2),
                        ),
                        Text('${settings.fontSize.toInt()}'),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: () => settings.setFontSize(settings.fontSize + 2),
                        ),
                      ],
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // معاينة النص
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                    style: TextStyle(
                      fontSize: settings.fontSize,
                      height: 2.0,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}