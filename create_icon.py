#!/usr/bin/env python3
"""
Simple script to create app icon using PIL
Run: python create_icon.py
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import math
    import os
except ImportError:
    print("Please install Pillow: pip install Pillow")
    exit(1)

def create_gradient_circle(size, center, radius, color1, color2):
    """Create a radial gradient circle"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    for i in range(radius):
        # Calculate color interpolation
        ratio = i / radius
        r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
        g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
        b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
        a = int(color1[3] * (1 - ratio) + color2[3] * ratio) if len(color1) > 3 else 255
        
        current_radius = radius - i
        draw.ellipse([center[0] - current_radius, center[1] - current_radius,
                     center[0] + current_radius, center[1] + current_radius],
                    fill=(r, g, b, a))
    
    return img

def draw_star(draw, x, y, size, color):
    """Draw a 5-pointed star"""
    points = []
    for i in range(10):
        angle = i * math.pi / 5
        if i % 2 == 0:
            radius = size
        else:
            radius = size * 0.4
        
        px = x + radius * math.cos(angle - math.pi / 2)
        py = y + radius * math.sin(angle - math.pi / 2)
        points.append((px, py))
    
    draw.polygon(points, fill=color)

def create_app_icon():
    """Create the main app icon"""
    size = 1024
    center = (size // 2, size // 2)
    
    # Create base image
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Background gradient (Islamic green)
    bg_gradient = create_gradient_circle(size, center, 480, 
                                       (76, 175, 80, 255),   # Light green
                                       (46, 125, 50, 255))   # Dark green
    img = Image.alpha_composite(img, bg_gradient)
    
    # Border
    draw.ellipse([32, 32, size-32, size-32], outline=(27, 94, 32), width=16)
    
    # Decorative circles
    draw.ellipse([center[0]-400, center[1]-400, center[0]+400, center[1]+400], 
                outline=(255, 215, 0, 80), width=4)
    draw.ellipse([center[0]-360, center[1]-360, center[0]+360, center[1]+360], 
                outline=(255, 215, 0, 50), width=2)
    
    # Try to load a font, fallback to default
    try:
        # Try to use a serif font for Arabic text
        font_large = ImageFont.truetype("arial.ttf", 120)
        font_small = ImageFont.truetype("arial.ttf", 24)
    except:
        try:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        except:
            font_large = None
            font_small = None
    
    # App title "قرآني"
    title_text = "قرآني"
    if font_large:
        # Get text size
        bbox = draw.textbbox((0, 0), title_text, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # Position text
        text_x = center[0] - text_width // 2
        text_y = 280
        
        # Draw text with shadow
        draw.text((text_x + 4, text_y + 4), title_text, fill=(27, 94, 32, 200), font=font_large)
        draw.text((text_x, text_y), title_text, fill=(255, 215, 0), font=font_large)
    
    # Open book
    book_center_x = center[0]
    book_center_y = 650
    book_width = 200
    book_height = 140
    
    # Book shadow
    shadow_y = book_center_y + book_height // 2 + 20
    draw.ellipse([book_center_x - book_width//2 - 10, shadow_y - 15,
                 book_center_x + book_width//2 + 10, shadow_y + 15],
                fill=(0, 0, 0, 50))
    
    # Left page
    left_page = [
        (book_center_x - book_width//2, book_center_y - book_height//2 + 20),
        (book_center_x - book_width//2, book_center_y + book_height//2 - 20),
        (book_center_x - 10, book_center_y + book_height//2),
        (book_center_x - 10, book_center_y - book_height//2)
    ]
    draw.polygon(left_page, fill=(255, 248, 225), outline=(224, 224, 224))
    
    # Right page
    right_page = [
        (book_center_x + 10, book_center_y - book_height//2),
        (book_center_x + 10, book_center_y + book_height//2),
        (book_center_x + book_width//2, book_center_y + book_height//2 - 20),
        (book_center_x + book_width//2, book_center_y - book_height//2 + 20)
    ]
    draw.polygon(right_page, fill=(255, 248, 225), outline=(224, 224, 224))
    
    # Book spine
    draw.rectangle([book_center_x - 10, book_center_y - book_height//2,
                   book_center_x + 10, book_center_y + book_height//2],
                  fill=(141, 110, 99))
    
    # Add some text lines on pages
    line_color = (224, 224, 224)
    for i in range(6):
        y_pos = book_center_y - book_height//2 + 40 + i * 15
        
        # Left page lines
        draw.rectangle([book_center_x - book_width//2 + 20, y_pos,
                       book_center_x - 20, y_pos + 2], fill=line_color)
        
        # Right page lines
        draw.rectangle([book_center_x + 20, y_pos,
                       book_center_x + book_width//2 - 20, y_pos + 2], fill=line_color)
    
    # Add decorative stars
    star_positions = [
        (200, 200),
        (824, 200),
        (150, 824),
        (874, 824)
    ]
    
    for star_x, star_y in star_positions:
        draw_star(draw, star_x, star_y, 25, (255, 215, 0, 200))
    
    # Outer glow
    draw.ellipse([16, 16, size-16, size-16], outline=(255, 215, 0, 100), width=8)
    
    return img

def main():
    """Main function"""
    # Create assets/icon directory if it doesn't exist
    os.makedirs('assets/icon', exist_ok=True)
    
    print("Creating app icon...")
    
    # Create main icon
    icon = create_app_icon()
    icon.save('assets/icon/app_icon.png', 'PNG', quality=100)
    print("✓ Created: assets/icon/app_icon.png")
    
    # Create smaller version for adaptive icon
    icon_small = icon.resize((432, 432), Image.Resampling.LANCZOS)
    icon_small.save('assets/icon/app_icon_foreground.png', 'PNG', quality=100)
    print("✓ Created: assets/icon/app_icon_foreground.png")
    
    # Create different sizes for testing
    sizes = [512, 256, 128, 64]
    for size in sizes:
        resized = icon.resize((size, size), Image.Resampling.LANCZOS)
        resized.save(f'assets/icon/app_icon_{size}.png', 'PNG', quality=100)
        print(f"✓ Created: assets/icon/app_icon_{size}.png")
    
    print("\n🎉 App icons created successfully!")
    print("\nFiles created:")
    print("- assets/icon/app_icon.png (1024x1024) - Main icon")
    print("- assets/icon/app_icon_foreground.png (432x432) - Adaptive icon")
    print("- assets/icon/app_icon_512.png (512x512)")
    print("- assets/icon/app_icon_256.png (256x256)")
    print("- assets/icon/app_icon_128.png (128x128)")
    print("- assets/icon/app_icon_64.png (64x64)")
    
    print("\nNext steps:")
    print("1. Run: flutter pub get")
    print("2. Run: flutter pub run flutter_launcher_icons")
    print("3. Build your app to see the new icon!")

if __name__ == "__main__":
    main()
