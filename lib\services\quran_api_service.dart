import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/quran_models.dart';

class QuranApiService {
  static const String _baseUrl = 'https://api.quran.com/api/v4';
  static const String _mp3QuranBaseUrl = 'https://www.mp3quran.net/api/v3';
  static const String _quranEncBaseUrl = 'https://quranenc.com/api/v1';

  // Get all chapters (surahs) - optimized version
  Future<List<Surah>> getAllSurahs() async {
    try {
      // Get chapters info first
      final chaptersResponse = await _makeApiCall('$_baseUrl/chapters');
      if (chaptersResponse == null) {
        return _getDefaultSurahs();
      }

      final chapters = chaptersResponse['chapters'] as List;
      List<Surah> surahs = [];

      // Process chapters in batches to avoid overwhelming the API
      for (int i = 0; i < chapters.length; i += 5) {
        final batch = chapters.skip(i).take(5);
        final batchFutures = batch.map((chapter) => _getSurahWithVerses(chapter['id']));
        final batchResults = await Future.wait(batchFutures);

        for (var surah in batchResults) {
          if (surah != null) {
            surahs.add(surah);
          }
        }

        // Small delay between batches to be respectful to the API
        if (i + 5 < chapters.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      return surahs.isEmpty ? _getDefaultSurahs() : surahs;
    } catch (e) {
      return _getDefaultSurahs();
    }
  }

  // Helper method for API calls with retry logic
  Future<Map<String, dynamic>?> _makeApiCall(String url, {int retries = 3}) async {
    for (int attempt = 0; attempt < retries; attempt++) {
      try {
        final response = await http.get(
          Uri.parse(url),
          headers: {'Accept': 'application/json'},
        ).timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          return json.decode(response.body);
        }
      } catch (e) {
        if (attempt == retries - 1) {
          return null;
        }
        await Future.delayed(Duration(seconds: attempt + 1));
      }
    }
    return null;
  }

  // Get specific surah with verses
  Future<Surah?> _getSurahWithVerses(int chapterId) async {
    try {
      // Get chapter info and verses concurrently
      final futures = await Future.wait([
        _makeApiCall('$_baseUrl/chapters/$chapterId'),
        _makeApiCall('$_baseUrl/verses/by_chapter/$chapterId?language=ar&words=false&translations=131'),
      ]);

      final chapterData = futures[0];
      final versesData = futures[1];

      if (chapterData != null && versesData != null) {
        final chapter = chapterData['chapter'];
        final verses = versesData['verses'] as List? ?? [];

        List<Ayah> ayahs = verses.map((verse) {
          return Ayah(
            number: verse['id'] ?? 0,
            text: verse['text_uthmani'] ?? verse['text_imlaei'] ?? '',
            numberInSurah: verse['verse_number'] ?? 0,
            juz: verse['juz_number'] ?? 1,
            manzil: verse['manzil_number'] ?? 1,
            page: verse['page_number'] ?? 1,
            ruku: verse['ruku_number'] ?? 1,
            hizbQuarter: verse['hizb_number'] ?? 1,
            sajda: verse['sajda_number'] != null,
          );
        }).toList();

        return Surah(
          number: chapter['id'] ?? chapterId,
          name: chapter['name_arabic'] ?? '',
          englishName: chapter['name_simple'] ?? '',
          englishNameTranslation: chapter['translated_name']?['name'] ?? '',
          revelationType: chapter['revelation_place'] == 'makkah' ? 'Meccan' : 'Medinan',
          numberOfAyahs: chapter['verses_count'] ?? ayahs.length,
          ayahs: ayahs,
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Get specific surah by number
  Future<Surah?> getSurah(int surahNumber) async {
    return await _getSurahWithVerses(surahNumber);
  }

  // Search verses
  Future<List<Map<String, dynamic>>> searchVerses(String query) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/search?q=$query&size=20&page=0'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['search']['results'] ?? []);
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  // Get available reciters from MP3Quran
  Future<List<Reciter>> getReciters() async {
    try {
      final response = await http.get(
        Uri.parse('$_mp3QuranBaseUrl/reciters'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final reciters = data['reciters'] as List;

        return reciters.map((reciter) {
          return Reciter(
            id: reciter['id'],
            name: reciter['name'],
            arabicName: reciter['name'],
            style: reciter['style'] ?? 'Murattal',
            baseUrl: reciter['Server'] ?? '',
          );
        }).toList();
      }
      return _getDefaultReciters();
    } catch (e) {
      return _getDefaultReciters();
    }
  }

  // Get tafsir for specific verse
  Future<List<Tafsir>> getTafsir(int surahNumber, int ayahNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/verses/by_chapter/$surahNumber?verse_number=$ayahNumber&translations=131,20,19'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final verses = data['verses'] as List;

        if (verses.isNotEmpty) {
          final verse = verses.first;
          final translations = verse['translations'] as List? ?? [];

          return translations.map((translation) {
            return Tafsir(
              ayahNumber: ayahNumber,
              text: translation['text'] ?? '',
              author: translation['resource_name'] ?? 'Unknown',
            );
          }).toList();
        }
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  // Get simplified tafsir from QuranEnc
  Future<String?> getSimplifiedTafsir(int surahNumber, int ayahNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$_quranEncBaseUrl/translation/aya/arabic_moyassar/$surahNumber/$ayahNumber'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['result']?['translation'];
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Get audio URL for surah
  String getAudioUrl(Reciter reciter, int surahNumber) {
    final formattedSurahNumber = surahNumber.toString().padLeft(3, '0');

    // Use MP3Quran format
    if (reciter.baseUrl.isNotEmpty) {
      return '${reciter.baseUrl}$formattedSurahNumber.mp3';
    }

    // Fallback to default format
    return 'https://server8.mp3quran.net/afs/$formattedSurahNumber.mp3';
  }

  // Default reciters if API fails
  List<Reciter> _getDefaultReciters() {
    return [
      Reciter(
        id: 1,
        name: 'Mishary Rashid Alafasy',
        arabicName: 'مشاري راشد العفاسي',
        style: 'Murattal',
        baseUrl: 'https://server8.mp3quran.net/afs/',
      ),
      Reciter(
        id: 2,
        name: 'Abdul Basit Abdul Samad',
        arabicName: 'عبد الباسط عبد الصمد',
        style: 'Murattal',
        baseUrl: 'https://server7.mp3quran.net/basit/Alafasy_128_kbps/',
      ),
      Reciter(
        id: 3,
        name: 'Saad Al Ghamdi',
        arabicName: 'سعد الغامدي',
        style: 'Murattal',
        baseUrl: 'https://server7.mp3quran.net/s_gmd/',
      ),
      Reciter(
        id: 4,
        name: 'Ahmed Al Ajmy',
        arabicName: 'أحمد العجمي',
        style: 'Murattal',
        baseUrl: 'https://server10.mp3quran.net/ajm/',
      ),
      Reciter(
        id: 5,
        name: 'Maher Al Mueaqly',
        arabicName: 'ماهر المعيقلي',
        style: 'Murattal',
        baseUrl: 'https://server12.mp3quran.net/maher/',
      ),
    ];
  }

  // Get Juz (Para) information
  Future<List<Map<String, dynamic>>> getJuzList() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/juzs'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['juzs'] ?? []);
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  // Get verses by Juz
  Future<List<Ayah>> getVersesByJuz(int juzNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/verses/by_juz/$juzNumber'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final verses = data['verses'] as List;

        return verses.map((verse) {
          return Ayah(
            number: verse['id'],
            text: verse['text_uthmani'] ?? verse['text_imlaei'] ?? '',
            numberInSurah: verse['verse_number'],
            juz: verse['juz_number'] ?? juzNumber,
            manzil: verse['manzil_number'] ?? 1,
            page: verse['page_number'] ?? 1,
            ruku: verse['ruku_number'] ?? 1,
            hizbQuarter: verse['hizb_number'] ?? 1,
            sajda: verse['sajda_number'] != null,
          );
        }).toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  // Get verses by page
  Future<List<Ayah>> getVersesByPage(int pageNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/verses/by_page/$pageNumber'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final verses = data['verses'] as List;

        return verses.map((verse) {
          return Ayah(
            number: verse['id'],
            text: verse['text_uthmani'] ?? verse['text_imlaei'] ?? '',
            numberInSurah: verse['verse_number'],
            juz: verse['juz_number'] ?? 1,
            manzil: verse['manzil_number'] ?? 1,
            page: verse['page_number'] ?? pageNumber,
            ruku: verse['ruku_number'] ?? 1,
            hizbQuarter: verse['hizb_number'] ?? 1,
            sajda: verse['sajda_number'] != null,
          );
        }).toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  // Default surahs if API fails
  List<Surah> _getDefaultSurahs() {
    return [
      Surah(
        number: 1,
        name: 'الفاتحة',
        englishName: 'Al-Fatihah',
        englishNameTranslation: 'The Opening',
        revelationType: 'Meccan',
        numberOfAyahs: 7,
        ayahs: [
          Ayah(number: 1, text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ', numberInSurah: 1, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
          Ayah(number: 2, text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ', numberInSurah: 2, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
          Ayah(number: 3, text: 'الرَّحْمَٰنِ الرَّحِيمِ', numberInSurah: 3, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
          Ayah(number: 4, text: 'مَالِكِ يَوْمِ الدِّينِ', numberInSurah: 4, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
          Ayah(number: 5, text: 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ', numberInSurah: 5, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
          Ayah(number: 6, text: 'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ', numberInSurah: 6, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
          Ayah(number: 7, text: 'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ', numberInSurah: 7, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        ],
      ),
      Surah(
        number: 112,
        name: 'الإخلاص',
        englishName: 'Al-Ikhlas',
        englishNameTranslation: 'The Sincerity',
        revelationType: 'Meccan',
        numberOfAyahs: 4,
        ayahs: [
          Ayah(number: 6230, text: 'قُلْ هُوَ اللَّهُ أَحَدٌ', numberInSurah: 1, juz: 30, manzil: 7, page: 604, ruku: 553, hizbQuarter: 240, sajda: false),
          Ayah(number: 6231, text: 'اللَّهُ الصَّمَدُ', numberInSurah: 2, juz: 30, manzil: 7, page: 604, ruku: 553, hizbQuarter: 240, sajda: false),
          Ayah(number: 6232, text: 'لَمْ يَلِدْ وَلَمْ يُولَدْ', numberInSurah: 3, juz: 30, manzil: 7, page: 604, ruku: 553, hizbQuarter: 240, sajda: false),
          Ayah(number: 6233, text: 'وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ', numberInSurah: 4, juz: 30, manzil: 7, page: 604, ruku: 553, hizbQuarter: 240, sajda: false),
        ],
      ),
    ];
  }
}
