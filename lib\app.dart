import 'package:flutter/material.dart';
import 'screens/home_screen.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تطبيق قرآني',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Noto Sans Arabic',
      ),
      builder: (context, widget) {
        // إضافة معالجة الأخطاء العامة
        ErrorWidget.builder = (FlutterErrorDetails details) {
          return Scaffold(
            body: Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'حدث خطأ غير متوقع',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      details.exception.toString(),
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamedAndRemoveUntil(
                          context, '/', (route) => false);
                      },
                      child: const Text('العودة للصفحة الرئيسية'),
                    ),
                  ],
                ),
              ),
            ),
          );
        };
        
        if (widget != null) return widget;
        throw StateError('widget is null');
      },
      home: const HomeScreen(),
    );
  }
}

