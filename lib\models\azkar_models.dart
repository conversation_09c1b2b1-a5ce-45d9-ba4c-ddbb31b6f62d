import 'package:hive/hive.dart';

part 'azkar_models.g.dart';

@HiveType(typeId: 5)
class ZikrCategory extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final String icon;

  @HiveField(4)
  final List<Zikr> azkar;

  ZikrCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.azkar,
  });

  factory ZikrCategory.fromJson(Map<String, dynamic> json) {
    return ZikrCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: json['icon'],
      azkar: (json['azkar'] as List)
          .map((zikr) => Zikr.fromJson(zikr))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'azkar': azkar.map((zikr) => zikr.toJson()).toList(),
    };
  }
}

@HiveType(typeId: 6)
class Zikr extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String text;

  @HiveField(2)
  final String translation;

  @HiveField(3)
  final int count;

  @HiveField(4)
  final String? reference;

  @HiveField(5)
  final String? benefit;

  @HiveField(6)
  final String? audioUrl;

  Zikr({
    required this.id,
    required this.text,
    required this.translation,
    required this.count,
    this.reference,
    this.benefit,
    this.audioUrl,
  });

  factory Zikr.fromJson(Map<String, dynamic> json) {
    return Zikr(
      id: json['id'],
      text: json['text'],
      translation: json['translation'],
      count: json['count'],
      reference: json['reference'],
      benefit: json['benefit'],
      audioUrl: json['audioUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'translation': translation,
      'count': count,
      'reference': reference,
      'benefit': benefit,
      'audioUrl': audioUrl,
    };
  }
}

@HiveType(typeId: 7)
class ZikrProgress extends HiveObject {
  @HiveField(0)
  final int zikrId;

  @HiveField(1)
  final int currentCount;

  @HiveField(2)
  final int totalCount;

  @HiveField(3)
  final DateTime lastUpdated;

  @HiveField(4)
  final bool isCompleted;

  ZikrProgress({
    required this.zikrId,
    required this.currentCount,
    required this.totalCount,
    required this.lastUpdated,
    required this.isCompleted,
  });

  Map<String, dynamic> toJson() {
    return {
      'zikrId': zikrId,
      'currentCount': currentCount,
      'totalCount': totalCount,
      'lastUpdated': lastUpdated.toIso8601String(),
      'isCompleted': isCompleted,
    };
  }
}

@HiveType(typeId: 8)
class TasbihCounter extends HiveObject {
  @HiveField(0)
  final String name;

  @HiveField(1)
  final int count;

  @HiveField(2)
  final int target;

  @HiveField(3)
  final DateTime createdAt;

  @HiveField(4)
  final DateTime lastUpdated;

  @HiveField(5)
  final String zikrText;

  TasbihCounter({
    required this.name,
    required this.count,
    required this.target,
    required this.createdAt,
    required this.lastUpdated,
    required this.zikrText,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'count': count,
      'target': target,
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'zikrText': zikrText,
    };
  }
}

// Pre-defined Azkar Categories
class AzkarData {
  static List<ZikrCategory> getDefaultCategories() {
    return [
      ZikrCategory(
        id: 1,
        name: 'أذكار الصباح',
        description: 'الأذكار التي تُقال في الصباح',
        icon: 'sunrise',
        azkar: _getMorningAzkar(),
      ),
      ZikrCategory(
        id: 2,
        name: 'أذكار المساء',
        description: 'الأذكار التي تُقال في المساء',
        icon: 'sunset',
        azkar: _getEveningAzkar(),
      ),
      ZikrCategory(
        id: 3,
        name: 'أذكار النوم',
        description: 'الأذكار التي تُقال قبل النوم',
        icon: 'sleep',
        azkar: _getSleepAzkar(),
      ),
      ZikrCategory(
        id: 4,
        name: 'أذكار الصلاة',
        description: 'الأذكار التي تُقال بعد الصلاة',
        icon: 'prayer',
        azkar: _getPrayerAzkar(),
      ),
      ZikrCategory(
        id: 5,
        name: 'أذكار متنوعة',
        description: 'أذكار مختلفة للمناسبات المتنوعة',
        icon: 'various',
        azkar: _getVariousAzkar(),
      ),
      ZikrCategory(
        id: 6,
        name: 'أذكار الطعام',
        description: 'الأذكار التي تُقال قبل وبعد الطعام',
        icon: 'food',
        azkar: _getFoodAzkar(),
      ),
      ZikrCategory(
        id: 7,
        name: 'أذكار السفر',
        description: 'الأذكار التي تُقال عند السفر',
        icon: 'travel',
        azkar: _getTravelAzkar(),
      ),
      ZikrCategory(
        id: 8,
        name: 'أذكار الاستيقاظ',
        description: 'الأذكار التي تُقال عند الاستيقاظ من النوم',
        icon: 'wake_up',
        azkar: _getWakeUpAzkar(),
      ),
    ];
  }

  static List<Zikr> _getMorningAzkar() {
    return [
      Zikr(
        id: 1,
        text: 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
        translation: 'أعوذ بالله من الشيطان الرجيم',
        count: 1,
        reference: 'القرآن الكريم',
        benefit: 'للاستعاذة من الشيطان',
      ),
      Zikr(
        id: 2,
        text: 'بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ',
        translation: 'بسم الله الرحمن الرحيم',
        count: 1,
        reference: 'القرآن الكريم',
        benefit: 'بركة البدء باسم الله',
      ),
      Zikr(
        id: 3,
        text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ * الرَّحْمَنِ الرَّحِيمِ * مَالِكِ يَوْمِ الدِّينِ * إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ * اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ * صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ',
        translation: 'سورة الفاتحة',
        count: 1,
        reference: 'القرآن الكريم',
        benefit: 'أم الكتاب وأعظم سورة في القرآن',
      ),
      Zikr(
        id: 4,
        text: 'قُلْ هُوَ اللَّهُ أَحَدٌ * اللَّهُ الصَّمَدُ * لَمْ يَلِدْ وَلَمْ يُولَدْ * وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ',
        translation: 'سورة الإخلاص',
        count: 3,
        reference: 'القرآن الكريم',
        benefit: 'تعدل ثلث القرآن',
      ),
      Zikr(
        id: 5,
        text: 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ * مِن شَرِّ مَا خَلَقَ * وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ * وَمِن شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ * وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ',
        translation: 'سورة الفلق',
        count: 3,
        reference: 'القرآن الكريم',
        benefit: 'للحماية من الشرور',
      ),
      Zikr(
        id: 6,
        text: 'قُلْ أَعُوذُ بِرَبِّ النَّاسِ * مَلِكِ النَّاسِ * إِلَهِ النَّاسِ * مِن شَرِّ الْوَسْوَاسِ الْخَنَّاسِ * الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ * مِنَ الْجِنَّةِ وَالنَّاسِ',
        translation: 'سورة الناس',
        count: 3,
        reference: 'القرآن الكريم',
        benefit: 'للحماية من وساوس الشياطين',
      ),
      Zikr(
        id: 7,
        text: 'اللَّهُمَّ بِكَ أَصْبَحْنَا وَبِكَ أَمْسَيْنَا وَبِكَ نَحْيَا وَبِكَ نَمُوتُ وَإِلَيْكَ النُّشُورُ',
        translation: 'اللهم بك أصبحنا وبك أمسينا وبك نحيا وبك نموت وإليك النشور',
        count: 1,
        reference: 'سنن الترمذي',
        benefit: 'إقرار بأن الحياة والموت بيد الله',
      ),
      Zikr(
        id: 8,
        text: 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ وَالْحَمْدُ لِلَّهِ لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
        translation: 'أصبحنا وأصبح الملك لله والحمد لله لا إله إلا الله وحده لا شريك له له الملك وله الحمد وهو على كل شيء قدير',
        count: 1,
        reference: 'صحيح مسلم',
        benefit: 'إقرار بوحدانية الله وملكه',
      ),
      Zikr(
        id: 9,
        text: 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ خَلَقْتَنِي وَأَنَا عَبْدُكَ وَأَنَا عَلَى عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْتُ أَعُوذُ بِكَ مِنْ شَرِّ مَا صَنَعْتُ أَبُوءُ لَكَ بِنِعْمَتِكَ عَلَيَّ وَأَبُوءُ بِذَنْبِي فَاغْفِرْ لِي فَإِنَّهُ لَا يَغْفِرُ الذُّنُوبَ إِلَّا أَنْتَ',
        translation: 'سيد الاستغفار',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'من قالها موقناً بها فمات من يومه دخل الجنة',
      ),
      Zikr(
        id: 10,
        text: 'اللَّهُمَّ عَافِنِي فِي بَدَنِي اللَّهُمَّ عَافِنِي فِي سَمْعِي اللَّهُمَّ عَافِنِي فِي بَصَرِي لَا إِلَهَ إِلَّا أَنْتَ',
        translation: 'اللهم عافني في بدني اللهم عافني في سمعي اللهم عافني في بصري لا إله إلا أنت',
        count: 3,
        reference: 'سنن أبي داود',
        benefit: 'دعاء للعافية في البدن والحواس',
      ),
    ];
  }

  static List<Zikr> _getEveningAzkar() {
    return [
      Zikr(
        id: 11,
        text: 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
        translation: 'أعوذ بالله من الشيطان الرجيم',
        count: 1,
        reference: 'القرآن الكريم',
        benefit: 'للاستعاذة من الشيطان',
      ),
      Zikr(
        id: 12,
        text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ * الرَّحْمَنِ الرَّحِيمِ * مَالِكِ يَوْمِ الدِّينِ * إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ * اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ * صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ',
        translation: 'سورة الفاتحة',
        count: 1,
        reference: 'القرآن الكريم',
        benefit: 'أم الكتاب وأعظم سورة في القرآن',
      ),
      Zikr(
        id: 13,
        text: 'قُلْ هُوَ اللَّهُ أَحَدٌ * اللَّهُ الصَّمَدُ * لَمْ يَلِدْ وَلَمْ يُولَدْ * وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ',
        translation: 'سورة الإخلاص',
        count: 3,
        reference: 'القرآن الكريم',
        benefit: 'تعدل ثلث القرآن',
      ),
      Zikr(
        id: 14,
        text: 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ * مِن شَرِّ مَا خَلَقَ * وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ * وَمِن شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ * وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ',
        translation: 'سورة الفلق',
        count: 3,
        reference: 'القرآن الكريم',
        benefit: 'للحماية من الشرور',
      ),
      Zikr(
        id: 15,
        text: 'قُلْ أَعُوذُ بِرَبِّ النَّاسِ * مَلِكِ النَّاسِ * إِلَهِ النَّاسِ * مِن شَرِّ الْوَسْوَاسِ الْخَنَّاسِ * الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ * مِنَ الْجِنَّةِ وَالنَّاسِ',
        translation: 'سورة الناس',
        count: 3,
        reference: 'القرآن الكريم',
        benefit: 'للحماية من وساوس الشياطين',
      ),
      Zikr(
        id: 16,
        text: 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ وَالْحَمْدُ لِلَّهِ لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
        translation: 'أمسينا وأمسى الملك لله والحمد لله لا إله إلا الله وحده لا شريك له له الملك وله الحمد وهو على كل شيء قدير',
        count: 1,
        reference: 'صحيح مسلم',
        benefit: 'إقرار بوحدانية الله وملكه',
      ),
      Zikr(
        id: 17,
        text: 'اللَّهُمَّ بِكَ أَمْسَيْنَا وَبِكَ أَصْبَحْنَا وَبِكَ نَحْيَا وَبِكَ نَمُوتُ وَإِلَيْكَ الْمَصِيرُ',
        translation: 'اللهم بك أمسينا وبك أصبحنا وبك نحيا وبك نموت وإليك المصير',
        count: 1,
        reference: 'سنن الترمذي',
        benefit: 'إقرار بأن الحياة والموت بيد الله',
      ),
      Zikr(
        id: 18,
        text: 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ خَلَقْتَنِي وَأَنَا عَبْدُكَ وَأَنَا عَلَى عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْتُ أَعُوذُ بِكَ مِنْ شَرِّ مَا صَنَعْتُ أَبُوءُ لَكَ بِنِعْمَتِكَ عَلَيَّ وَأَبُوءُ بِذَنْبِي فَاغْفِرْ لِي فَإِنَّهُ لَا يَغْفِرُ الذُّنُوبَ إِلَّا أَنْتَ',
        translation: 'سيد الاستغفار',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'من قالها موقناً بها فمات من يومه دخل الجنة',
      ),
      Zikr(
        id: 19,
        text: 'اللَّهُمَّ عَافِنِي فِي بَدَنِي اللَّهُمَّ عَافِنِي فِي سَمْعِي اللَّهُمَّ عَافِنِي فِي بَصَرِي لَا إِلَهَ إِلَّا أَنْتَ',
        translation: 'اللهم عافني في بدني اللهم عافني في سمعي اللهم عافني في بصري لا إله إلا أنت',
        count: 3,
        reference: 'سنن أبي داود',
        benefit: 'دعاء للعافية في البدن والحواس',
      ),
      Zikr(
        id: 20,
        text: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي الدُّنْيَا وَالْآخِرَةِ',
        translation: 'اللهم إني أسألك العفو والعافية في الدنيا والآخرة',
        count: 1,
        reference: 'سنن ابن ماجه',
        benefit: 'دعاء للعفو والعافية',
      ),
    ];
  }

  static List<Zikr> _getSleepAzkar() {
    return [
      Zikr(
        id: 21,
        text: 'بِاسْمِكَ اللَّهُمَّ أَمُوتُ وَأَحْيَا',
        translation: 'باسمك اللهم أموت وأحيا',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'دعاء عند النوم',
      ),
      Zikr(
        id: 22,
        text: 'اللَّهُمَّ قِنِي عَذَابَكَ يَوْمَ تَبْعَثُ عِبَادَكَ',
        translation: 'اللهم قني عذابك يوم تبعث عبادك',
        count: 3,
        reference: 'سنن أبي داود',
        benefit: 'دعاء للحماية من عذاب الآخرة',
      ),
      Zikr(
        id: 23,
        text: 'اللَّهُمَّ أَسْلَمْتُ نَفْسِي إِلَيْكَ وَفَوَّضْتُ أَمْرِي إِلَيْكَ وَأَلْجَأْتُ ظَهْرِي إِلَيْكَ رَغْبَةً وَرَهْبَةً إِلَيْكَ لَا مَلْجَأَ وَلَا مَنْجَا مِنْكَ إِلَّا إِلَيْكَ آمَنْتُ بِكِتَابِكَ الَّذِي أَنْزَلْتَ وَبِنَبِيِّكَ الَّذِي أَرْسَلْتَ',
        translation: 'اللهم أسلمت نفسي إليك وفوضت أمري إليك وألجأت ظهري إليك رغبة ورهبة إليك لا ملجأ ولا منجا منك إلا إليك آمنت بكتابك الذي أنزلت وبنبيك الذي أرسلت',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'من مات من ليلته مات على الفطرة',
      ),
      Zikr(
        id: 24,
        text: 'سُبْحَانَ اللَّهِ',
        translation: 'سبحان الله',
        count: 33,
        reference: 'صحيح البخاري',
        benefit: 'تسبيح قبل النوم',
      ),
      Zikr(
        id: 25,
        text: 'الْحَمْدُ لِلَّهِ',
        translation: 'الحمد لله',
        count: 33,
        reference: 'صحيح البخاري',
        benefit: 'حمد قبل النوم',
      ),
      Zikr(
        id: 26,
        text: 'اللَّهُ أَكْبَرُ',
        translation: 'الله أكبر',
        count: 34,
        reference: 'صحيح البخاري',
        benefit: 'تكبير قبل النوم',
      ),
      Zikr(
        id: 27,
        text: 'قُلْ هُوَ اللَّهُ أَحَدٌ * اللَّهُ الصَّمَدُ * لَمْ يَلِدْ وَلَمْ يُولَدْ * وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ',
        translation: 'سورة الإخلاص',
        count: 3,
        reference: 'القرآن الكريم',
        benefit: 'تعدل ثلث القرآن',
      ),
      Zikr(
        id: 28,
        text: 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ * مِن شَرِّ مَا خَلَقَ * وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ * وَمِن شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ * وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ',
        translation: 'سورة الفلق',
        count: 3,
        reference: 'القرآن الكريم',
        benefit: 'للحماية من الشرور',
      ),
      Zikr(
        id: 29,
        text: 'قُلْ أَعُوذُ بِرَبِّ النَّاسِ * مَلِكِ النَّاسِ * إِلَهِ النَّاسِ * مِن شَرِّ الْوَسْوَاسِ الْخَنَّاسِ * الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ * مِنَ الْجِنَّةِ وَالنَّاسِ',
        translation: 'سورة الناس',
        count: 3,
        reference: 'القرآن الكريم',
        benefit: 'للحماية من وساوس الشياطين',
      ),
      Zikr(
        id: 30,
        text: 'اللَّهُمَّ إِنَّكَ خَلَقْتَ نَفْسِي وَأَنْتَ تَوَفَّاهَا لَكَ مَمَاتُهَا وَمَحْيَاهَا إِنْ أَحْيَيْتَهَا فَاحْفَظْهَا وَإِنْ أَمَتَّهَا فَاغْفِرْ لَهَا اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَافِيَةَ',
        translation: 'اللهم إنك خلقت نفسي وأنت توفاها لك مماتها ومحياها إن أحييتها فاحفظها وإن أمتها فاغفر لها اللهم إني أسألك العافية',
        count: 1,
        reference: 'صحيح مسلم',
        benefit: 'دعاء شامل قبل النوم',
      ),
    ];
  }

  static List<Zikr> _getPrayerAzkar() {
    return [
      Zikr(
        id: 31,
        text: 'أَسْتَغْفِرُ اللَّهَ',
        translation: 'أستغفر الله',
        count: 3,
        reference: 'صحيح مسلم',
        benefit: 'استغفار بعد الصلاة',
      ),
      Zikr(
        id: 32,
        text: 'اللَّهُمَّ أَنْتَ السَّلَامُ وَمِنْكَ السَّلَامُ تَبَارَكْتَ يَا ذَا الْجَلَالِ وَالْإِكْرَامِ',
        translation: 'اللهم أنت السلام ومنك السلام تباركت يا ذا الجلال والإكرام',
        count: 1,
        reference: 'صحيح مسلم',
        benefit: 'دعاء بعد التسليم من الصلاة',
      ),
      Zikr(
        id: 33,
        text: 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
        translation: 'لا إله إلا الله وحده لا شريك له له الملك وله الحمد وهو على كل شيء قدير',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'توحيد الله بعد الصلاة',
      ),
      Zikr(
        id: 34,
        text: 'سُبْحَانَ اللَّهِ',
        translation: 'سبحان الله',
        count: 33,
        reference: 'صحيح مسلم',
        benefit: 'تسبيح بعد الصلاة',
      ),
      Zikr(
        id: 35,
        text: 'الْحَمْدُ لِلَّهِ',
        translation: 'الحمد لله',
        count: 33,
        reference: 'صحيح مسلم',
        benefit: 'حمد بعد الصلاة',
      ),
      Zikr(
        id: 36,
        text: 'اللَّهُ أَكْبَرُ',
        translation: 'الله أكبر',
        count: 34,
        reference: 'صحيح مسلم',
        benefit: 'تكبير بعد الصلاة',
      ),
      Zikr(
        id: 37,
        text: 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ يُحْيِي وَيُمِيتُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
        translation: 'لا إله إلا الله وحده لا شريك له له الملك وله الحمد يحيي ويميت وهو على كل شيء قدير',
        count: 1,
        reference: 'سنن الترمذي',
        benefit: 'توحيد شامل بعد الصلاة',
      ),
      Zikr(
        id: 38,
        text: 'اللَّهُمَّ لَا مَانِعَ لِمَا أَعْطَيْتَ وَلَا مُعْطِيَ لِمَا مَنَعْتَ وَلَا يَنْفَعُ ذَا الْجَدِّ مِنْكَ الْجَدُّ',
        translation: 'اللهم لا مانع لما أعطيت ولا معطي لما منعت ولا ينفع ذا الجد منك الجد',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'إقرار بقدرة الله المطلقة',
      ),
      Zikr(
        id: 39,
        text: 'اللَّهُمَّ أَعِنِّي عَلَى ذِكْرِكَ وَشُكْرِكَ وَحُسْنِ عِبَادَتِكَ',
        translation: 'اللهم أعني على ذكرك وشكرك وحسن عبادتك',
        count: 1,
        reference: 'سنن أبي داود',
        benefit: 'دعاء للإعانة على العبادة',
      ),
      Zikr(
        id: 40,
        text: 'رَبِّ اغْفِرْ لِي ذَنْبِي وَخَطَئِي وَجَهْلِي وَمَا أَسْرَرْتُ وَمَا أَعْلَنْتُ وَمَا أَنْتَ أَعْلَمُ بِهِ مِنِّي',
        translation: 'رب اغفر لي ذنبي وخطئي وجهلي وما أسررت وما أعلنت وما أنت أعلم به مني',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'دعاء شامل للمغفرة',
      ),
    ];
  }

  static List<Zikr> _getVariousAzkar() {
    return [
      Zikr(
        id: 41,
        text: 'لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ',
        translation: 'لا حول ولا قوة إلا بالله',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'كنز من كنوز الجنة',
      ),
      Zikr(
        id: 42,
        text: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
        translation: 'سبحان الله وبحمده',
        count: 100,
        reference: 'صحيح مسلم',
        benefit: 'من قالها في يوم مائة مرة حطت خطاياه',
      ),
      Zikr(
        id: 43,
        text: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ سُبْحَانَ اللَّهِ الْعَظِيمِ',
        translation: 'سبحان الله وبحمده سبحان الله العظيم',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'كلمتان خفيفتان على اللسان ثقيلتان في الميزان',
      ),
      Zikr(
        id: 44,
        text: 'لَا إِلَهَ إِلَّا اللَّهُ',
        translation: 'لا إله إلا الله',
        count: 100,
        reference: 'صحيح مسلم',
        benefit: 'أفضل الذكر',
      ),
      Zikr(
        id: 45,
        text: 'اللَّهُمَّ صَلِّ وَسَلِّمْ عَلَى نَبِيِّنَا مُحَمَّدٍ',
        translation: 'اللهم صل وسلم على نبينا محمد',
        count: 10,
        reference: 'سنن الترمذي',
        benefit: 'من صلى علي واحدة صلى الله عليه عشراً',
      ),
      Zikr(
        id: 46,
        text: 'أَسْتَغْفِرُ اللَّهَ الْعَظِيمَ الَّذِي لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ وَأَتُوبُ إِلَيْهِ',
        translation: 'أستغفر الله العظيم الذي لا إله إلا هو الحي القيوم وأتوب إليه',
        count: 1,
        reference: 'سنن أبي داود',
        benefit: 'من قالها غفر له وإن كان فر من الزحف',
      ),
      Zikr(
        id: 47,
        text: 'رَبِّ اغْفِرْ لِي وَتُبْ عَلَيَّ إِنَّكَ أَنْتَ التَّوَّابُ الرَّحِيمُ',
        translation: 'رب اغفر لي وتب علي إنك أنت التواب الرحيم',
        count: 100,
        reference: 'سنن أبي داود',
        benefit: 'دعاء للمغفرة والتوبة',
      ),
      Zikr(
        id: 48,
        text: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ مِنَ الْخَيْرِ كُلِّهِ عَاجِلِهِ وَآجِلِهِ مَا عَلِمْتُ مِنْهُ وَمَا لَمْ أَعْلَمْ',
        translation: 'اللهم إني أسألك من الخير كله عاجله وآجله ما علمت منه وما لم أعلم',
        count: 1,
        reference: 'صحيح ابن حبان',
        benefit: 'دعاء جامع للخير',
      ),
      Zikr(
        id: 49,
        text: 'اللَّهُمَّ أَصْلِحْ لِي دِينِي الَّذِي هُوَ عِصْمَةُ أَمْرِي وَأَصْلِحْ لِي دُنْيَايَ الَّتِي فِيهَا مَعَاشِي',
        translation: 'اللهم أصلح لي ديني الذي هو عصمة أمري وأصلح لي دنياي التي فيها معاشي',
        count: 1,
        reference: 'صحيح مسلم',
        benefit: 'دعاء لإصلاح الدين والدنيا',
      ),
      Zikr(
        id: 50,
        text: 'حَسْبُنَا اللَّهُ وَنِعْمَ الْوَكِيلُ',
        translation: 'حسبنا الله ونعم الوكيل',
        count: 7,
        reference: 'القرآن الكريم',
        benefit: 'للتوكل على الله',
      ),
    ];
  }

  static List<Zikr> _getFoodAzkar() {
    return [
      Zikr(
        id: 51,
        text: 'بِسْمِ اللَّهِ',
        translation: 'بسم الله',
        count: 1,
        reference: 'سنن أبي داود',
        benefit: 'دعاء قبل الطعام',
      ),
      Zikr(
        id: 52,
        text: 'اللَّهُمَّ بَارِكْ لَنَا فِيمَا رَزَقْتَنَا وَقِنَا عَذَابَ النَّارِ',
        translation: 'اللهم بارك لنا فيما رزقتنا وقنا عذاب النار',
        count: 1,
        reference: 'سنن الترمذي',
        benefit: 'دعاء قبل الطعام',
      ),
      Zikr(
        id: 53,
        text: 'الْحَمْدُ لِلَّهِ الَّذِي أَطْعَمَنَا وَسَقَانَا وَجَعَلَنَا مُسْلِمِينَ',
        translation: 'الحمد لله الذي أطعمنا وسقانا وجعلنا مسلمين',
        count: 1,
        reference: 'سنن أبي داود',
        benefit: 'دعاء بعد الطعام',
      ),
      Zikr(
        id: 54,
        text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        translation: 'الحمد لله رب العالمين',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'حمد بعد الطعام',
      ),
    ];
  }

  static List<Zikr> _getTravelAzkar() {
    return [
      Zikr(
        id: 55,
        text: 'سُبْحَانَ الَّذِي سَخَّرَ لَنَا هَذَا وَمَا كُنَّا لَهُ مُقْرِنِينَ وَإِنَّا إِلَى رَبِّنَا لَمُنْقَلِبُونَ',
        translation: 'سبحان الذي سخر لنا هذا وما كنا له مقرنين وإنا إلى ربنا لمنقلبون',
        count: 1,
        reference: 'سنن أبي داود',
        benefit: 'دعاء ركوب المركبة',
      ),
      Zikr(
        id: 56,
        text: 'اللَّهُمَّ إِنَّا نَسْأَلُكَ فِي سَفَرِنَا هَذَا الْبِرَّ وَالتَّقْوَى وَمِنَ الْعَمَلِ مَا تَرْضَى',
        translation: 'اللهم إنا نسألك في سفرنا هذا البر والتقوى ومن العمل ما ترضى',
        count: 1,
        reference: 'سنن الترمذي',
        benefit: 'دعاء السفر',
      ),
      Zikr(
        id: 57,
        text: 'اللَّهُمَّ اطْوِ لَنَا الْأَرْضَ وَهَوِّنْ عَلَيْنَا السَّفَرَ',
        translation: 'اللهم اطو لنا الأرض وهون علينا السفر',
        count: 1,
        reference: 'سنن الترمذي',
        benefit: 'دعاء لتسهيل السفر',
      ),
    ];
  }

  static List<Zikr> _getWakeUpAzkar() {
    return [
      Zikr(
        id: 58,
        text: 'الْحَمْدُ لِلَّهِ الَّذِي أَحْيَانَا بَعْدَ مَا أَمَاتَنَا وَإِلَيْهِ النُّشُورُ',
        translation: 'الحمد لله الذي أحيانا بعد ما أماتنا وإليه النشور',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'دعاء الاستيقاظ من النوم',
      ),
      Zikr(
        id: 59,
        text: 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
        translation: 'لا إله إلا الله وحده لا شريك له له الملك وله الحمد وهو على كل شيء قدير',
        count: 1,
        reference: 'صحيح البخاري',
        benefit: 'توحيد عند الاستيقاظ',
      ),
      Zikr(
        id: 60,
        text: 'سُبْحَانَ اللَّهِ وَالْحَمْدُ لِلَّهِ وَلَا إِلَهَ إِلَّا اللَّهُ وَاللَّهُ أَكْبَرُ',
        translation: 'سبحان الله والحمد لله ولا إله إلا الله والله أكبر',
        count: 1,
        reference: 'سنن الترمذي',
        benefit: 'تسبيح وتحميد عند الاستيقاظ',
      ),
    ];
  }
}
