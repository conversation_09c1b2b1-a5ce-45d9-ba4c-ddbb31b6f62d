// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hadith_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HadithCategoryAdapter extends TypeAdapter<HadithCategory> {
  @override
  final int typeId = 9;

  @override
  HadithCategory read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HadithCategory(
      id: fields[0] as int,
      name: fields[1] as String,
      description: fields[2] as String,
      icon: fields[3] as String,
      hadiths: (fields[4] as List).cast<Hadith>(),
    );
  }

  @override
  void write(BinaryWriter writer, HadithCategory obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.icon)
      ..writeByte(4)
      ..write(obj.hadiths);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HadithCategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HadithAdapter extends TypeAdapter<Hadith> {
  @override
  final int typeId = 10;

  @override
  Hadith read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Hadith(
      id: fields[0] as int,
      arabicText: fields[1] as String,
      translation: fields[2] as String,
      narrator: fields[3] as String,
      source: fields[4] as String,
      grade: fields[5] as String,
      explanation: fields[6] as String?,
      keywords: (fields[7] as List).cast<String>(),
      isFavorite: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Hadith obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.arabicText)
      ..writeByte(2)
      ..write(obj.translation)
      ..writeByte(3)
      ..write(obj.narrator)
      ..writeByte(4)
      ..write(obj.source)
      ..writeByte(5)
      ..write(obj.grade)
      ..writeByte(6)
      ..write(obj.explanation)
      ..writeByte(7)
      ..write(obj.keywords)
      ..writeByte(8)
      ..write(obj.isFavorite);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HadithAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HadithOfTheDayAdapter extends TypeAdapter<HadithOfTheDay> {
  @override
  final int typeId = 11;

  @override
  HadithOfTheDay read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HadithOfTheDay(
      hadith: fields[0] as Hadith,
      date: fields[1] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, HadithOfTheDay obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.hadith)
      ..writeByte(1)
      ..write(obj.date);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HadithOfTheDayAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
