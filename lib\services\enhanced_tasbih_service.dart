import 'dart:convert';
import 'package:hive/hive.dart';
import '../models/azkar_models.dart';
import '../utils/error_handler.dart';
import '../utils/app_constants.dart';

class EnhancedTasbihService {
  static const String _tasbihBoxName = AppConstants.tasbihBoxName;
  static const String _progressBoxName = AppConstants.zikrProgressBoxName;

  late Box<TasbihCounter> _tasbihBox;
  late Box<ZikrProgress> _progressBox;
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _tasbihBox = await Hive.openBox<TasbihCounter>(_tasbihBoxName);
      _progressBox = await Hive.openBox<ZikrProgress>(_progressBoxName);
      _isInitialized = true;

      // Create default counters if none exist
      if (_tasbihBox.isEmpty) {
        await _createDefaultCounters();
      }
    } catch (e) {
      ErrorHandler.logError('Failed to initialize TasbihService: $e', null);
      rethrow;
    }
  }

  /// Get all tasbih counters
  Future<List<TasbihCounter>> getAllCounters() async {
    await initialize();
    return _tasbihBox.values.toList();
  }

  /// Create a new tasbih counter
  Future<TasbihCounter> createCounter({
    required String name,
    required String zikrText,
    int target = 33,
  }) async {
    await initialize();

    final counter = TasbihCounter(
      name: name,
      count: 0,
      target: target,
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
      zikrText: zikrText,
    );

    await _tasbihBox.add(counter);

    ErrorHandler.logInfo('Created new tasbih counter: $name');
    return counter;
  }

  /// Update counter count
  Future<void> updateCounter(int index, int newCount) async {
    await initialize();

    if (index < 0 || index >= _tasbihBox.length) {
      throw ArgumentError('Invalid counter index: $index');
    }

    final counter = _tasbihBox.getAt(index);
    if (counter == null) return;

    final updatedCounter = TasbihCounter(
      name: counter.name,
      count: newCount.clamp(0, AppConstants.maxTasbihCount),
      target: counter.target,
      createdAt: counter.createdAt,
      lastUpdated: DateTime.now(),
      zikrText: counter.zikrText,
    );

    await _tasbihBox.putAt(index, updatedCounter);

    // Update progress if target is reached
    if (newCount >= counter.target) {
      await _updateProgress(counter.name, newCount, counter.target);
    }
  }

  /// Increment counter
  Future<int> incrementCounter(int index) async {
    await initialize();

    if (index < 0 || index >= _tasbihBox.length) {
      throw ArgumentError('Invalid counter index: $index');
    }

    final counter = _tasbihBox.getAt(index);
    if (counter == null) return 0;

    final newCount = (counter.count + 1).clamp(0, AppConstants.maxTasbihCount);
    await updateCounter(index, newCount);

    return newCount;
  }

  /// Decrement counter
  Future<int> decrementCounter(int index) async {
    await initialize();

    if (index < 0 || index >= _tasbihBox.length) {
      throw ArgumentError('Invalid counter index: $index');
    }

    final counter = _tasbihBox.getAt(index);
    if (counter == null) return 0;

    final newCount = (counter.count - 1).clamp(0, AppConstants.maxTasbihCount);
    await updateCounter(index, newCount);

    return newCount;
  }

  /// Reset counter
  Future<void> resetCounter(int index) async {
    await initialize();
    await updateCounter(index, 0);
    ErrorHandler.logError('Reset counter at index: $index', null);
  }

  /// Reset all counters
  Future<void> resetAllCounters() async {
    await initialize();

    for (int i = 0; i < _tasbihBox.length; i++) {
      await resetCounter(i);
    }

    ErrorHandler.logInfo('Reset all tasbih counters');
  }

  /// Delete counter
  Future<void> deleteCounter(int index) async {
    await initialize();

    if (index < 0 || index >= _tasbihBox.length) {
      throw ArgumentError('Invalid counter index: $index');
    }

    final counter = _tasbihBox.getAt(index);
    await _tasbihBox.deleteAt(index);

    ErrorHandler.logError('Deleted counter: ${counter?.name}', null);
  }

  /// Update counter target
  Future<void> updateCounterTarget(int index, int newTarget) async {
    await initialize();

    if (index < 0 || index >= _tasbihBox.length) {
      throw ArgumentError('Invalid counter index: $index');
    }

    final counter = _tasbihBox.getAt(index);
    if (counter == null) return;

    final updatedCounter = TasbihCounter(
      name: counter.name,
      count: counter.count,
      target: newTarget.clamp(1, AppConstants.maxTasbihCount),
      createdAt: counter.createdAt,
      lastUpdated: DateTime.now(),
      zikrText: counter.zikrText,
    );

    await _tasbihBox.putAt(index, updatedCounter);
  }

  /// Update counter name
  Future<void> updateCounterName(int index, String newName) async {
    await initialize();

    if (index < 0 || index >= _tasbihBox.length) {
      throw ArgumentError('Invalid counter index: $index');
    }

    final counter = _tasbihBox.getAt(index);
    if (counter == null) return;

    final updatedCounter = TasbihCounter(
      name: newName,
      count: counter.count,
      target: counter.target,
      createdAt: counter.createdAt,
      lastUpdated: DateTime.now(),
      zikrText: counter.zikrText,
    );

    await _tasbihBox.putAt(index, updatedCounter);
  }

  /// Update counter zikr text
  Future<void> updateCounterZikr(int index, String newZikrText) async {
    await initialize();

    if (index < 0 || index >= _tasbihBox.length) {
      throw ArgumentError('Invalid counter index: $index');
    }

    final counter = _tasbihBox.getAt(index);
    if (counter == null) return;

    final updatedCounter = TasbihCounter(
      name: counter.name,
      count: counter.count,
      target: counter.target,
      createdAt: counter.createdAt,
      lastUpdated: DateTime.now(),
      zikrText: newZikrText,
    );

    await _tasbihBox.putAt(index, updatedCounter);
  }

  /// Get counter by index
  Future<TasbihCounter?> getCounter(int index) async {
    await initialize();

    if (index < 0 || index >= _tasbihBox.length) {
      return null;
    }

    return _tasbihBox.getAt(index);
  }

  /// Get counter by name
  Future<TasbihCounter?> getCounterByName(String name) async {
    await initialize();

    for (var counter in _tasbihBox.values) {
      if (counter.name == name) {
        return counter;
      }
    }

    return null;
  }

  /// Get counters statistics
  Future<Map<String, dynamic>> getStatistics() async {
    await initialize();

    final counters = _tasbihBox.values.toList();
    int totalCount = 0;
    int completedTargets = 0;
    int totalTargets = 0;

    for (var counter in counters) {
      totalCount += counter.count;
      totalTargets += counter.target;
      if (counter.count >= counter.target) {
        completedTargets++;
      }
    }

    return {
      'totalCounters': counters.length,
      'totalCount': totalCount,
      'totalTargets': totalTargets,
      'completedTargets': completedTargets,
      'completionRate': counters.isNotEmpty ? (completedTargets / counters.length) * 100 : 0,
      'averageCount': counters.isNotEmpty ? totalCount / counters.length : 0,
    };
  }

  /// Get daily progress
  Future<List<ZikrProgress>> getDailyProgress() async {
    await initialize();

    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return _progressBox.values
        .where((progress) =>
            progress.lastUpdated.isAfter(startOfDay) &&
            progress.lastUpdated.isBefore(endOfDay))
        .toList();
  }

  /// Get weekly progress
  Future<List<ZikrProgress>> getWeeklyProgress() async {
    await initialize();

    final today = DateTime.now();
    final weekAgo = today.subtract(const Duration(days: 7));

    return _progressBox.values
        .where((progress) => progress.lastUpdated.isAfter(weekAgo))
        .toList();
  }

  /// Get monthly progress
  Future<List<ZikrProgress>> getMonthlyProgress() async {
    await initialize();

    final today = DateTime.now();
    final monthAgo = today.subtract(const Duration(days: 30));

    return _progressBox.values
        .where((progress) => progress.lastUpdated.isAfter(monthAgo))
        .toList();
  }

  /// Export counters data
  Future<String> exportCounters() async {
    await initialize();

    final counters = _tasbihBox.values.toList();
    final data = {
      'counters': counters.map((counter) => counter.toJson()).toList(),
      'exportDate': DateTime.now().toIso8601String(),
      'version': AppConstants.appVersion,
    };

    return json.encode(data);
  }

  /// Import counters data
  Future<void> importCounters(String jsonData) async {
    await initialize();

    try {
      final data = json.decode(jsonData);
      final countersData = data['counters'] as List;

      // Clear existing counters
      await _tasbihBox.clear();

      // Import new counters
      for (var counterData in countersData) {
        final counter = TasbihCounter(
          name: counterData['name'] ?? '',
          count: counterData['count'] ?? 0,
          target: counterData['target'] ?? 33,
          createdAt: DateTime.parse(counterData['createdAt'] ?? DateTime.now().toIso8601String()),
          lastUpdated: DateTime.parse(counterData['lastUpdated'] ?? DateTime.now().toIso8601String()),
          zikrText: counterData['zikrText'] ?? '',
        );

        await _tasbihBox.add(counter);
      }

      ErrorHandler.logInfo('Successfully imported ${countersData.length} counters');
    } catch (e) {
      ErrorHandler.logError('Failed to import counters: $e', null);
      rethrow;
    }
  }

  /// Create default counters
  Future<void> _createDefaultCounters() async {
    final defaultCounters = [
      {
        'name': 'سبحان الله',
        'zikrText': 'سُبْحانَ اللَّه',
        'target': 33,
      },
      {
        'name': 'الحمد لله',
        'zikrText': 'الْحَمْدُ لِلَّه',
        'target': 33,
      },
      {
        'name': 'الله أكبر',
        'zikrText': 'اللَّهُ أَكْبَرُ',
        'target': 34,
      },
      {
        'name': 'لا إله إلا الله',
        'zikrText': 'لَا إِلَهَ إِلَّا اللَّه',
        'target': 100,
      },
      {
        'name': 'استغفار',
        'zikrText': 'أَسْغِفِ اللَّهَ ظِيْنَاتِي',
        'target': 100,
      },
    ];

    for (var counterData in defaultCounters) {
      await createCounter(
        name: counterData['name'] as String,
        zikrText: counterData['zikrText'] as String,
        target: counterData['target'] as int,
      );
    }

    ErrorHandler.logError('Created ${defaultCounters.length} default tasbih counters', null);
  }

  /// Update progress tracking
  Future<void> _updateProgress(String counterName, int currentCount, int target) async {
    final progress = ZikrProgress(
      zikrId: counterName.hashCode,
      currentCount: currentCount,
      totalCount: target,
      lastUpdated: DateTime.now(),
      isCompleted: currentCount >= target,
    );

    await _progressBox.add(progress);
  }

  /// Get completion percentage for counter
  double getCompletionPercentage(TasbihCounter counter) {
    if (counter.target <= 0) return 0.0;
    return (counter.count / counter.target * 100).clamp(0.0, 100.0);
  }

  /// Check if counter is completed
  bool isCounterCompleted(TasbihCounter counter) {
    return counter.count >= counter.target;
  }

  /// Get remaining count for counter
  int getRemainingCount(TasbihCounter counter) {
    return (counter.target - counter.count).clamp(0, counter.target);
  }

  /// Get counters sorted by completion
  Future<List<TasbihCounter>> getCountersSortedByCompletion() async {
    final counters = await getAllCounters();

    counters.sort((a, b) {
      final aCompletion = getCompletionPercentage(a);
      final bCompletion = getCompletionPercentage(b);
      return bCompletion.compareTo(aCompletion);
    });

    return counters;
  }

  /// Get counters sorted by last updated
  Future<List<TasbihCounter>> getCountersSortedByLastUpdated() async {
    final counters = await getAllCounters();

    counters.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));

    return counters;
  }

  /// Dispose resources
  Future<void> dispose() async {
    if (_isInitialized) {
      await _tasbihBox.close();
      await _progressBox.close();
      _isInitialized = false;
    }
  }
}
