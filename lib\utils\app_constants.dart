/// App Constants and Configuration
class AppConstants {
  // App Information
  static const String appName = 'قرآني';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق قرآني شامل للقرآن الكريم والأذكار والأحاديث النبوية';
  
  // API Configuration
  static const String quranApiBaseUrl = 'https://api.alquran.cloud/v1';
  static const String hadithApiBaseUrl = 'https://api.hadith.gading.dev';
  static const String prayerTimesApiBaseUrl = 'https://api.aladhan.com/v1';
  
  // Database Configuration
  static const String databaseName = 'quraan.db';
  static const int databaseVersion = 1;
  
  // Hive Box Names
  static const String settingsBoxName = 'settings';
  static const String surahBoxName = 'surahs';
  static const String bookmarkBoxName = 'bookmarks';
  static const String zikrProgressBoxName = 'zikr_progress';
  static const String tasbihBoxName = 'tasbih_counters';
  static const String hadithOfTheDayBoxName = 'hadith_of_the_day';
  
  // UI Configuration
  static const double defaultFontSize = 18.0;
  static const double minFontSize = 12.0;
  static const double maxFontSize = 26.0;
  
  // Colors
  static const int primaryColorValue = 0xFF2E7D32;
  static const int primaryColorDarkValue = 0xFF4CAF50;
  
  // Notification Configuration
  static const String morningAzkarChannelId = 'morning_azkar';
  static const String eveningAzkarChannelId = 'evening_azkar';
  static const String hadithReminderChannelId = 'hadith_reminder';
  static const String quranReminderChannelId = 'quran_reminder';
  static const String prayerTimesChannelId = 'prayer_times';
  
  // Default Settings
  static const bool defaultDarkMode = false;
  static const bool defaultNotificationsEnabled = true;
  static const bool defaultAutoPlay = false;
  static const bool defaultShowTranslation = true;
  static const bool defaultShowTafsir = false;
  
  // Prayer Times
  static const List<String> prayerNames = [
    'الفجر',
    'الشروق',
    'الظهر',
    'العصر',
    'المغرب',
    'العشاء'
  ];
  
  // Quran Configuration
  static const int totalSurahs = 114;
  static const int totalAyahs = 6236;
  static const int totalJuz = 30;
  
  // Error Messages
  static const String networkErrorMessage = 'تعذر الاتصال بالإنترنت';
  static const String databaseErrorMessage = 'خطأ في قاعدة البيانات';
  static const String generalErrorMessage = 'حدث خطأ غير متوقع';
  
  // Success Messages
  static const String bookmarkAddedMessage = 'تم إضافة العلامة المرجعية';
  static const String bookmarkRemovedMessage = 'تم حذف العلامة المرجعية';
  static const String settingsSavedMessage = 'تم حفظ الإعدادات';
  
  // Validation
  static const int maxBookmarkNote = 500;
  static const int maxTasbihCount = 9999;
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100; // MB
  
  // Audio Configuration
  static const List<String> supportedAudioFormats = ['mp3', 'wav', 'aac'];
  static const double defaultVolume = 0.8;
  
  // Sharing Configuration
  static const String shareTextPrefix = 'من تطبيق قرآني:\n';
  static const String appStoreUrl = 'https://play.google.com/store/apps/details?id=com.islamicapp.quraan';
  
  // Privacy and Terms
  static const String privacyPolicyUrl = 'https://islamicapp.com/privacy';
  static const String termsOfServiceUrl = 'https://islamicapp.com/terms';
  static const String supportEmail = '<EMAIL>';
  
  // Feature Flags
  static const bool enableAnalytics = false;
  static const bool enableCrashReporting = true;
  static const bool enableOfflineMode = true;
  static const bool enableDarkMode = true;
  static const bool enableNotifications = true;
  
  // Rate Limiting
  static const Duration apiCallDelay = Duration(milliseconds: 500);
  static const int maxRetryAttempts = 3;
  
  // Localization
  static const String defaultLocale = 'ar';
  static const List<String> supportedLocales = ['ar', 'en'];
}