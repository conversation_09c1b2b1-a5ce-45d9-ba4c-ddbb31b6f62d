import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';
import '../models/quran_models.dart';
import '../screens/quran/surah_reading_screen.dart';

class SurahListItem extends StatelessWidget {
  final Surah surah;
  final VoidCallback? onDownload;

  const SurahListItem({
    super.key,
    required this.surah,
    this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 4,
        shadowColor: const Color(0xFF4CAF50).withAlpha((255 * 0.3).round()),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                const Color(0xFF4CAF50).withAlpha((255 * 0.1).round()),
                const Color(0xFF66BB6A).withAlpha((255 * 0.05).round()),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF4CAF50).withAlpha((255 * 0.3).round()),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  '${surah.number}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    surah.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Text(
                  '📖',
                  style: TextStyle(fontSize: 20),
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(surah.englishNameTranslation),
                Text(
                  '${surah.numberOfAyahs} آية • ${surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية'}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.play_arrow),
                  onPressed: () {
                    quranProvider.playSurah(surah.number);
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.download),
                  onPressed: onDownload, // Use the callback
                ),
              ],
            ),
            onTap: () async {
              final quranProvider = Provider.of<QuranProvider>(context, listen: false);
              quranProvider.setCurrentSurah(surah);

              // Load full surah data if needed
              Surah? fullSurah = surah;
              if (surah.ayahs.isEmpty) {
                fullSurah = await quranProvider.loadSurah(surah.number);
              }

              if (context.mounted) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SurahReadingScreen(surah: fullSurah!),
                  ),
                );
              } else if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('فشل في تحميل السورة'),
                  ),
                );
              }
            },
          ),
        ),
      ),
    );
  }
}