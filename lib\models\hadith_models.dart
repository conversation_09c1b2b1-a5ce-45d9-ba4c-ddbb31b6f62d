import 'package:hive/hive.dart';

part 'hadith_models.g.dart';

@HiveType(typeId: 9)
class HadithCategory extends HiveObject {
  @HiveField(0)
  final int id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String description;
  
  @HiveField(3)
  final String icon;
  
  @HiveField(4)
  final List<Hadith> hadiths;

  HadithCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.hadiths,
  });

  factory HadithCategory.fromJson(Map<String, dynamic> json) {
    return HadithCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: json['icon'],
      hadiths: (json['hadiths'] as List)
          .map((hadith) => Hadith.fromJson(hadith))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'hadiths': hadiths.map((hadith) => hadith.toJson()).toList(),
    };
  }
}

@HiveType(typeId: 10)
class Hadith extends HiveObject {
  @HiveField(0)
  final int id;
  
  @HiveField(1)
  final String arabicText;
  
  @HiveField(2)
  final String translation;
  
  @HiveField(3)
  final String narrator;
  
  @HiveField(4)
  final String source;
  
  @HiveField(5)
  final String grade;
  
  @HiveField(6)
  final String? explanation;
  
  @HiveField(7)
  final List<String> keywords;
  
  @HiveField(8)
  final bool isFavorite;

  Hadith({
    required this.id,
    required this.arabicText,
    required this.translation,
    required this.narrator,
    required this.source,
    required this.grade,
    this.explanation,
    required this.keywords,
    this.isFavorite = false,
  });

  factory Hadith.fromJson(Map<String, dynamic> json) {
    return Hadith(
      id: json['id'],
      arabicText: json['arabicText'],
      translation: json['translation'],
      narrator: json['narrator'],
      source: json['source'],
      grade: json['grade'],
      explanation: json['explanation'],
      keywords: List<String>.from(json['keywords'] ?? []),
      isFavorite: json['isFavorite'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabicText': arabicText,
      'translation': translation,
      'narrator': narrator,
      'source': source,
      'grade': grade,
      'explanation': explanation,
      'keywords': keywords,
      'isFavorite': isFavorite,
    };
  }
}

@HiveType(typeId: 11)
class HadithOfTheDay extends HiveObject {
  @HiveField(0)
  final Hadith hadith;
  
  @HiveField(1)
  final DateTime date;

  HadithOfTheDay({
    required this.hadith,
    required this.date,
  });

  Map<String, dynamic> toJson() {
    return {
      'hadith': hadith.toJson(),
      'date': date.toIso8601String(),
    };
  }
}

// Pre-defined Hadith Categories and Data
class HadithData {
  static List<HadithCategory> getDefaultCategories() {
    return [
      HadithCategory(
        id: 1,
        name: 'الأخلاق والآداب',
        description: 'أحاديث عن الأخلاق الحسنة والآداب الإسلامية',
        icon: 'ethics',
        hadiths: _getEthicsHadiths(),
      ),
      HadithCategory(
        id: 2,
        name: 'الصلاة',
        description: 'أحاديث عن الصلاة وأحكامها',
        icon: 'prayer',
        hadiths: _getPrayerHadiths(),
      ),
      HadithCategory(
        id: 3,
        name: 'الصيام',
        description: 'أحاديث عن الصيام وفضائله',
        icon: 'fasting',
        hadiths: _getFastingHadiths(),
      ),
      HadithCategory(
        id: 4,
        name: 'بر الوالدين',
        description: 'أحاديث عن بر الوالدين وطاعتهما',
        icon: 'parents',
        hadiths: _getParentsHadiths(),
      ),
      HadithCategory(
        id: 5,
        name: 'الذكر والدعاء',
        description: 'أحاديث عن فضل الذكر والدعاء',
        icon: 'dhikr',
        hadiths: _getDhikrHadiths(),
      ),
      HadithCategory(
        id: 6,
        name: 'الجنة والنار',
        description: 'أحاديث عن الجنة والنار والآخرة',
        icon: 'afterlife',
        hadiths: _getAfterlifeHadiths(),
      ),
    ];
  }

  static List<Hadith> _getEthicsHadiths() {
    return [
      Hadith(
        id: 1,
        arabicText: 'إِنَّمَا بُعِثْتُ لِأُتَمِّمَ مَكَارِمَ الْأَخْلَاقِ',
        translation: 'إنما بُعثت لأتمم مكارم الأخلاق',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'الأدب المفرد للبخاري',
        grade: 'صحيح',
        keywords: ['أخلاق', 'مكارم', 'بعثة'],
        explanation: 'يبين هذا الحديث أن الهدف الأساسي من بعثة النبي صلى الله عليه وسلم هو إتمام مكارم الأخلاق',
      ),
      Hadith(
        id: 2,
        arabicText: 'الْمُؤْمِنُ لَيْسَ بِالطَّعَّانِ وَلَا اللَّعَّانِ وَلَا الْفَاحِشِ وَلَا الْبَذِيءِ',
        translation: 'المؤمن ليس بالطعان ولا اللعان ولا الفاحش ولا البذيء',
        narrator: 'عبد الله بن مسعود رضي الله عنه',
        source: 'سنن الترمذي',
        grade: 'صحيح',
        keywords: ['مؤمن', 'أخلاق', 'لسان'],
        explanation: 'يوضح هذا الحديث صفات المؤمن الحق وأنه يتجنب سوء الكلام',
      ),
    ];
  }

  static List<Hadith> _getPrayerHadiths() {
    return [
      Hadith(
        id: 3,
        arabicText: 'الصَّلَاةُ عِمَادُ الدِّينِ',
        translation: 'الصلاة عماد الدين',
        narrator: 'عمر بن الخطاب رضي الله عنه',
        source: 'البيهقي',
        grade: 'حسن',
        keywords: ['صلاة', 'عماد', 'دين'],
        explanation: 'يبين هذا الحديث أهمية الصلاة في الإسلام وأنها أساس الدين',
      ),
    ];
  }

  static List<Hadith> _getFastingHadiths() {
    return [
      Hadith(
        id: 4,
        arabicText: 'الصِّيَامُ جُنَّةٌ',
        translation: 'الصيام جُنة',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        grade: 'صحيح',
        keywords: ['صيام', 'جنة', 'وقاية'],
        explanation: 'يوضح هذا الحديث أن الصيام وقاية من المعاصي والنار',
      ),
    ];
  }

  static List<Hadith> _getParentsHadiths() {
    return [
      Hadith(
        id: 5,
        arabicText: 'رِضَا الرَّبِّ فِي رِضَا الْوَالِدِ وَسَخَطُ الرَّبِّ فِي سَخَطِ الْوَالِدِ',
        translation: 'رضا الرب في رضا الوالد وسخط الرب في سخط الوالد',
        narrator: 'عبد الله بن عمرو رضي الله عنهما',
        source: 'سنن الترمذي',
        grade: 'صحيح',
        keywords: ['والدين', 'رضا', 'بر'],
        explanation: 'يبين هذا الحديث أهمية رضا الوالدين وأن رضا الله مرتبط برضاهما',
      ),
    ];
  }

  static List<Hadith> _getDhikrHadiths() {
    return [
      Hadith(
        id: 6,
        arabicText: 'أَلَا أُنَبِّئُكُمْ بِخَيْرِ أَعْمَالِكُمْ وَأَزْكَاهَا عِنْدَ مَلِيكِكُمْ؟ ذِكْرُ اللَّهِ',
        translation: 'ألا أنبئكم بخير أعمالكم وأزكاها عند مليككم؟ ذكر الله',
        narrator: 'أبو الدرداء رضي الله عنه',
        source: 'سنن الترمذي',
        grade: 'حسن',
        keywords: ['ذكر', 'خير', 'أعمال'],
        explanation: 'يوضح هذا الحديث فضل ذكر الله وأنه من أفضل الأعمال',
      ),
    ];
  }

  static List<Hadith> _getAfterlifeHadiths() {
    return [
      Hadith(
        id: 7,
        arabicText: 'الدُّنْيَا سِجْنُ الْمُؤْمِنِ وَجَنَّةُ الْكَافِرِ',
        translation: 'الدنيا سجن المؤمن وجنة الكافر',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح مسلم',
        grade: 'صحيح',
        keywords: ['دنيا', 'آخرة', 'مؤمن'],
        explanation: 'يوضح هذا الحديث طبيعة الحياة الدنيا بالنسبة للمؤمن والكافر',
      ),
    ];
  }
}
