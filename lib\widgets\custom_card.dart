import 'package:flutter/material.dart';

class CustomAnimatedCard extends StatefulWidget {
  final Widget child;
  final List<Color> gradientColors;
  final double elevation;
  final Duration animationDuration;

  const CustomAnimatedCard({
    super.key,
    required this.child,
    this.gradientColors = const [Color(0xFF6A1B9A), Color(0xFF26A69A)],
    this.elevation = 8.0,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  // ignore: library_private_types_in_public_api
  _CustomAnimatedCardState createState() => _CustomAnimatedCardState();
}

class _CustomAnimatedCardState extends State<CustomAnimatedCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: widget.animationDuration,
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: widget.gradientColors,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.purpleAccent.withAlpha((255 * (_isHovered ? 0.3 : 0.1)).round()),
              blurRadius: _isHovered ? 20 : 10,
              spreadRadius: _isHovered ? 2 : 1,
            ),
          ],
        ),
        child: Material(
          type: MaterialType.transparency,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {},
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: widget.child,
            ),
          ),
        ),
      ),
    );
  }
}