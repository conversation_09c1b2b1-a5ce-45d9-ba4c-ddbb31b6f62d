import 'package:flutter/material.dart';
import 'prayer_times_service.dart';

class SmartNotificationService {
  static final SmartNotificationService _instance = SmartNotificationService._internal();
  factory SmartNotificationService() => _instance;
  SmartNotificationService._internal();

  static SmartNotificationService get instance => _instance;

  final PrayerTimesService _prayerTimesService = PrayerTimesService();

  /// جدولة تذكير القراءة اليومي
  Future<void> scheduleReadingReminder({
    int hour = 20, // 8 PM
    int minute = 0,
    String title = 'وقت القراءة 📖',
    String body = 'حان وقت قراءة القرآن الكريم',
  }) async {
    try {
      final now = DateTime.now();
      var scheduledDate = DateTime(
        now.year,
        now.month,
        now.day,
        hour,
        minute,
      );

      // إذا كان الوقت قد مضى اليوم، جدول للغد
      if (scheduledDate.isBefore(now)) {
        scheduledDate = scheduledDate.add(const Duration(days: 1));
      }

      // محاكاة جدولة الإشعار - يمكن تطبيقها لاحقاً
      print('📅 Scheduling reading reminder: $title at ${scheduledDate.toString()}');

      print('✅ Reading reminder scheduled for ${scheduledDate.toString()}');
    } catch (e) {
      print('❌ Error scheduling reading reminder: $e');
    }
  }

  /// جدولة تذكيرات أوقات الصلاة
  Future<void> schedulePrayerReminders({
    double latitude = 21.3891, // مكة المكرمة
    double longitude = 39.8579,
  }) async {
    try {
      final prayerData = await _prayerTimesService.getPrayerTimes(
        latitude: latitude,
        longitude: longitude,
      );

      if (prayerData == null) {
        print('❌ Could not get prayer times');
        return;
      }

      final prayerTimes = _prayerTimesService.parsePrayerTimesArabic(prayerData);
      final prayers = [
        {'name': 'الفجر', 'key': 'Fajr'},
        {'name': 'الظهر', 'key': 'Dhuhr'},
        {'name': 'العصر', 'key': 'Asr'},
        {'name': 'المغرب', 'key': 'Maghrib'},
        {'name': 'العشاء', 'key': 'Isha'},
      ];

      for (int i = 0; i < prayers.length; i++) {
        final prayerName = prayers[i]['name']!;
        final prayerKey = prayers[i]['key']!;
        
        if (prayerTimes.containsKey(prayerKey)) {
          final prayerTime = prayerTimes[prayerKey]!;
          final scheduledDate = _getPrayerDateTime(prayerTime);

          // محاكاة جدولة إشعار الصلاة
          print('📅 Scheduling prayer reminder: $prayerName at ${scheduledDate.toString()}');

          print('✅ Prayer reminder scheduled for $prayerName at ${scheduledDate.toString()}');
        }
      }
    } catch (e) {
      print('❌ Error scheduling prayer reminders: $e');
    }
  }

  /// جدولة تذكير الأذكار
  Future<void> scheduleAzkarReminders() async {
    try {
      final azkarReminders = [
        {
          'id': 3001,
          'title': 'أذكار الصباح 🌅',
          'body': 'حان وقت أذكار الصباح',
          'hour': 6,
          'minute': 0,
        },
        {
          'id': 3002,
          'title': 'أذكار المساء 🌆',
          'body': 'حان وقت أذكار المساء',
          'hour': 17,
          'minute': 0,
        },
        {
          'id': 3003,
          'title': 'أذكار النوم 🌙',
          'body': 'لا تنس أذكار النوم',
          'hour': 22,
          'minute': 0,
        },
      ];

      for (final reminder in azkarReminders) {
        final now = DateTime.now();
        var scheduledDate = DateTime(
          now.year,
          now.month,
          now.day,
          reminder['hour'] as int,
          reminder['minute'] as int,
        );

        if (scheduledDate.isBefore(now)) {
          scheduledDate = scheduledDate.add(const Duration(days: 1));
        }

        // محاكاة جدولة تذكير الأذكار
        print('📅 Scheduling azkar reminder: ${reminder['title']} at ${scheduledDate.toString()}');

        print('✅ Azkar reminder scheduled: ${reminder['title']}');
      }
    } catch (e) {
      print('❌ Error scheduling azkar reminders: $e');
    }
  }

  /// جدولة تذكير أسبوعي لختم القرآن
  Future<void> scheduleWeeklyQuranReminder() async {
    try {
      final now = DateTime.now();
      // جدولة كل يوم جمعة الساعة 2 ظهراً
      var nextFriday = now.add(Duration(days: (5 - now.weekday) % 7));
      if (nextFriday.weekday != DateTime.friday || 
          (nextFriday.weekday == DateTime.friday && now.hour >= 14)) {
        nextFriday = nextFriday.add(const Duration(days: 7));
      }
      
      final scheduledDate = DateTime(
        nextFriday.year,
        nextFriday.month,
        nextFriday.day,
        14, // 2 PM
        0,
      );

      // محاكاة جدولة التذكير الأسبوعي
      print('📅 Scheduling weekly reminder at ${scheduledDate.toString()}');

      print('✅ Weekly Quran reminder scheduled for ${scheduledDate.toString()}');
    } catch (e) {
      print('❌ Error scheduling weekly reminder: $e');
    }
  }

  /// جدولة جميع التذكيرات الذكية
  Future<void> scheduleAllSmartReminders({
    bool readingReminders = true,
    bool prayerReminders = true,
    bool azkarReminders = true,
    bool weeklyReminders = true,
    double? latitude,
    double? longitude,
  }) async {
    try {
      print('🔔 Scheduling all smart reminders...');

      if (readingReminders) {
        await scheduleReadingReminder();
      }

      if (prayerReminders) {
        await schedulePrayerReminders(
          latitude: latitude ?? 21.3891,
          longitude: longitude ?? 39.8579,
        );
      }

      if (azkarReminders) {
        await scheduleAzkarReminders();
      }

      if (weeklyReminders) {
        await scheduleWeeklyQuranReminder();
      }

      print('🎉 All smart reminders scheduled successfully');
    } catch (e) {
      print('❌ Error scheduling smart reminders: $e');
    }
  }

  /// إلغاء جميع التذكيرات
  Future<void> cancelAllReminders() async {
    try {
      // محاكاة إلغاء التذكيرات
      print('🚫 Cancelling all reminders...');
      print('✅ All reminders cancelled');
    } catch (e) {
      print('❌ Error cancelling reminders: $e');
    }
  }

  /// تحويل وقت الصلاة إلى DateTime
  DateTime _getPrayerDateTime(TimeOfDay prayerTime) {
    final now = DateTime.now();
    var prayerDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      prayerTime.hour,
      prayerTime.minute,
    );

    // إذا كان وقت الصلاة قد مضى اليوم، جدول للغد
    if (prayerDateTime.isBefore(now)) {
      prayerDateTime = prayerDateTime.add(const Duration(days: 1));
    }

    return prayerDateTime;
  }

  /// إرسال إشعار فوري للاختبار
  Future<void> sendTestNotification() async {
    try {
      // محاكاة إرسال إشعار اختبار
      print('🔔 Test notification: اختبار الإشعارات ✅');
      print('✅ Test notification sent successfully');
    } catch (e) {
      print('❌ Error sending test notification: $e');
    }
  }

  /// الحصول على إحصائيات التذكيرات
  Map<String, dynamic> getReminderStats() {
    return {
      'reading_reminders': 1,
      'prayer_reminders': 5,
      'azkar_reminders': 3,
      'weekly_reminders': 1,
      'total_reminders': 10,
      'last_updated': DateTime.now().toIso8601String(),
    };
  }
}