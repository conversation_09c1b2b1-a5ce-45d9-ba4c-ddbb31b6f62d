import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/settings_provider.dart';
import '../../services/notification_service.dart';
import '../../services/audio_service.dart';
import 'theme_settings_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF607D8B), Color(0xFF78909C)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.settings_rounded,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'الإعدادات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF607D8B), Color(0xFF78909C)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return ListView(
            children: [
              _buildAppearanceSection(context, settingsProvider),
              _buildAudioSection(context, settingsProvider),
              _buildReadingSection(context, settingsProvider),
              _buildNotificationSection(context, settingsProvider),
              _buildAboutSection(context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAppearanceSection(BuildContext context, SettingsProvider settingsProvider) {
    return _buildSection(
      title: 'المظهر',
      children: [
        ListTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF6A1B9A), Color(0xFF9C27B0)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.palette,
              color: Colors.white,
              size: 20,
            ),
          ),
          title: const Text('تخصيص المظهر'),
          subtitle: const Text('الثيمات والألوان وحجم الخط'),
          trailing: const Icon(Icons.arrow_back_ios), // تغيير الاتجاه للـ RTL
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ThemeSettingsScreen(),
              ),
            );
          },
        ),
        const Divider(height: 1),
        SwitchListTile(
          title: const Text('الوضع الليلي'),
          subtitle: const Text('تفعيل الوضع الليلي لراحة العين'),
          value: settingsProvider.isDarkMode,
          onChanged: (value) {
            settingsProvider.setDarkMode(value);
          },
        ),
        ListTile(
          title: const Text('حجم الخط'),
          subtitle: Text('الحجم الحالي: ${settingsProvider.getFontSizeCategory()}'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.remove),
                onPressed: () {
                  final newSize = settingsProvider.getPreviousFontSize();
                  settingsProvider.setFontSize(newSize);
                },
              ),
              Text('${settingsProvider.fontSize.toInt()}'),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: () {
                  final newSize = settingsProvider.getNextFontSize();
                  settingsProvider.setFontSize(newSize);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAudioSection(BuildContext context, SettingsProvider settingsProvider) {
    return _buildSection(
      title: 'الصوتيات',
      children: [
        ListTile(
          title: const Text('القارئ المفضل'),
          subtitle: Text(_getCurrentReciterName(settingsProvider.selectedReciterId)),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () {
            _showReciterSelectionDialog(context, settingsProvider);
          },
        ),
        SwitchListTile(
          title: const Text('التشغيل التلقائي'),
          subtitle: const Text('تشغيل الآية التالية تلقائياً'),
          value: settingsProvider.autoPlay,
          onChanged: (value) {
            settingsProvider.setAutoPlay(value);
          },
        ),
      ],
    );
  }

  Widget _buildReadingSection(BuildContext context, SettingsProvider settingsProvider) {
    return _buildSection(
      title: 'القراءة',
      children: [
        SwitchListTile(
          title: const Text('إظهار الترجمة'),
          subtitle: const Text('عرض ترجمة معاني الآيات'),
          value: settingsProvider.showTranslation,
          onChanged: (value) {
            settingsProvider.setShowTranslation(value);
          },
        ),
        SwitchListTile(
          title: const Text('إظهار التفسير'),
          subtitle: const Text('عرض تفسير الآيات'),
          value: settingsProvider.showTafsir,
          onChanged: (value) {
            settingsProvider.setShowTafsir(value);
          },
        ),
      ],
    );
  }

  Widget _buildNotificationSection(BuildContext context, SettingsProvider settingsProvider) {
    return _buildSection(
      title: 'الإشعارات',
      children: [
        SwitchListTile(
          title: const Text('تفعيل الإشعارات'),
          subtitle: const Text('تلقي تذكيرات للأذكار والقرآن'),
          value: settingsProvider.notificationsEnabled,
          onChanged: (value) {
            settingsProvider.setNotificationsEnabled(value);
          },
        ),
        if (settingsProvider.notificationsEnabled) ...[
          ListTile(
            title: const Text('وقت أذكار الصباح'),
            subtitle: Text(settingsProvider.formatTime(settingsProvider.morningAzkarTime)),
            trailing: const Icon(Icons.access_time),
            onTap: () {
              _showTimePicker(
                context,
                settingsProvider.morningAzkarTime,
                (time) => settingsProvider.setMorningAzkarTime(time),
              );
            },
          ),
          ListTile(
            title: const Text('وقت أذكار المساء'),
            subtitle: Text(settingsProvider.formatTime(settingsProvider.eveningAzkarTime)),
            trailing: const Icon(Icons.access_time),
            onTap: () {
              _showTimePicker(
                context,
                settingsProvider.eveningAzkarTime,
                (time) => settingsProvider.setEveningAzkarTime(time),
              );
            },
          ),
          ListTile(
            title: const Text('وقت تذكير حديث اليوم'),
            subtitle: Text(settingsProvider.formatTime(settingsProvider.hadithReminderTime)),
            trailing: const Icon(Icons.access_time),
            onTap: () {
              _showTimePicker(
                context,
                settingsProvider.hadithReminderTime,
                (time) => settingsProvider.setHadithReminderTime(time),
              );
            },
          ),
          ListTile(
            title: const Text('وقت تذكير قراءة القرآن'),
            subtitle: Text(settingsProvider.formatTime(settingsProvider.quranReminderTime)),
            trailing: const Icon(Icons.access_time),
            onTap: () {
              _showTimePicker(
                context,
                settingsProvider.quranReminderTime,
                (time) => settingsProvider.setQuranReminderTime(time),
              );
            },
          ),
          ListTile(
            title: const Text('اختبار الإشعارات'),
            subtitle: const Text('إرسال إشعار تجريبي'),
            trailing: const Icon(Icons.send),
            onTap: () {
              NotificationService.instance.showTestNotification();
            },
          ),
        ],
      ],
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return _buildSection(
      title: 'حول التطبيق',
      children: [
        // معلومات التطبيق
        _buildAppInfoCard(context),
        const SizedBox(height: 16),

        // قسم الإهداء
        _buildDedicationCard(context),
        const SizedBox(height: 16),

        // الإجراءات
        _buildActionButtons(context),
      ],
    );
  }

  Widget _buildAppInfoCard(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.1),
            Theme.of(context).primaryColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          // أيقونة التطبيق
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Icon(
              Icons.menu_book_rounded,
              color: Colors.white,
              size: 40,
            ),
          ),
          const SizedBox(height: 16),

          // اسم التطبيق
          const Text(
            'تطبيق القرآن الكريم',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // وصف التطبيق
          const Text(
            'تطبيق شامل للقرآن الكريم والأذكار والأحاديث النبوية',
            style: TextStyle(
              fontSize: 16,
              color: Colors.black54,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // معلومات النسخة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildInfoItem(Icons.info_outline, 'النسخة', '1.0.0'),
              _buildInfoItem(Icons.calendar_today, 'التاريخ', '2024'),
              _buildInfoItem(Icons.code, 'التقنية', 'Flutter'),
            ],
          ),
          const SizedBox(height: 16),

          // معلومات المطور
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.person,
                  color: Colors.blue,
                  size: 24,
                ),
                SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المطور',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                    Text(
                      'Ehab Khalifa',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Builder(
      builder: (context) => Column(
        children: [
          Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    ),
    );
  }

  Widget _buildDedicationCard(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFFF8F9FA),
            Color(0xFFE9ECEF),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // أيقونة الإهداء
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.green,
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.mosque,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(height: 16),

          // العنوان الرئيسي
          const Text(
            'صدقة جارية على روح',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.green,
              letterSpacing: 0.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),

          // اسم المتوفى مع تأطير
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            decoration: const BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.all(Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                  color: Colors.green,
                  blurRadius: 6,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: const Text(
              'الحاج مختار',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                letterSpacing: 1.0,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // دعاء الرحمة
          const Text(
            'رحمه الله وأسكنه فسيح جناته',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
              fontStyle: FontStyle.italic,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // فاصل زخرفي
          const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 30,
                height: 1,
                child: ColoredBox(color: Colors.green),
              ),
              SizedBox(width: 8),
              Icon(
                Icons.star,
                color: Colors.green,
                size: 12,
              ),
              SizedBox(width: 8),
              SizedBox(
                width: 30,
                height: 1,
                child: ColoredBox(color: Colors.green),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // آية قرآنية
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.all(Radius.circular(12)),
            ),
            child: const Column(
              children: [
                Text(
                  '﴿وَالَّذِينَ آمَنُوا وَاتَّبَعَتْهُمْ ذُرِّيَّتُهُم بِإِيمَانٍ أَلْحَقْنَا بِهِمْ ذُرِّيَّتَهُمْ﴾',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    height: 1.6,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),
                Text(
                  'سورة الطور - آية 21',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // دعاء ختامي
          const Text(
            'اللهم اغفر له وارحمه واجعل هذا العمل في ميزان حسناته',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black54,
              fontStyle: FontStyle.italic,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // إعادة تعيين الإعدادات
        _buildActionTile(
          context: context,
          icon: Icons.restore,
          title: 'إعادة تعيين الإعدادات',
          subtitle: 'استعادة الإعدادات الافتراضية',
          onTap: () => _showResetDialog(context),
          color: Colors.orange,
        ),

        // تقييم التطبيق
        _buildActionTile(
          context: context,
          icon: Icons.star,
          title: 'تقييم التطبيق',
          subtitle: 'قيم التطبيق في المتجر',
          onTap: () => _rateApp(context),
          color: Colors.amber,
        ),

        // مشاركة التطبيق
        _buildActionTile(
          context: context,
          icon: Icons.share,
          title: 'مشاركة التطبيق',
          subtitle: 'شارك التطبيق مع الأصدقاء',
          onTap: () => _shareApp(context),
          color: Colors.blue,
        ),

        // معلومات إضافية
        _buildActionTile(
          context: context,
          icon: Icons.info_outline,
          title: 'معلومات إضافية',
          subtitle: 'تفاصيل التطبيق والمطور',
          onTap: () => _showAppDetails(context),
          color: Colors.purple,
        ),
      ],
    );
  }

  Widget _buildActionTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(subtitle),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey[600],
        ),
        onTap: onTap,
      ),
    );
  }

  void _rateApp(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('شكراً لك! سيتم توجيهك لتقييم التطبيق قريباً'),
        backgroundColor: Colors.amber,
      ),
    );
  }

  void _shareApp(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ رابط التطبيق للمشاركة'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showAppDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات التطبيق'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('📱 تطبيق القرآن الكريم'),
            SizedBox(height: 8),
            Text('🔢 الإصدار: 1.0.0'),
            SizedBox(height: 8),
            Text('📅 تاريخ الإصدار: 2024'),
            SizedBox(height: 8),
            Text('👨‍💻 المطور: Ehab Khalifa'),
            SizedBox(height: 8),
            Text('💻 تم تطويره بـ Flutter'),
            SizedBox(height: 8),
            Text('🎯 تطبيق شامل للقرآن والأذكار'),
            SizedBox(height: 12),
            Divider(),
            SizedBox(height: 8),
            Text('💚 صدقة جارية على روح الحاج مختار'),
            SizedBox(height: 4),
            Text('🤲 الله يرحمه ويسكنه فسيح جناته'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({required String title, required List<Widget> children}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
        ),
        ...children,
        const Divider(),
      ],
    );
  }

  String _getCurrentReciterName(int reciterId) {
    final reciters = AudioService.getDefaultReciters();
    final reciter = reciters.firstWhere(
      (r) => r.id == reciterId,
      orElse: () => reciters.first,
    );
    return reciter.arabicName;
  }

  void _showReciterSelectionDialog(BuildContext context, SettingsProvider settingsProvider) {
    final reciters = AudioService.getDefaultReciters();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر القارئ المفضل'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: reciters.length,
            itemBuilder: (context, index) {
              final reciter = reciters[index];
              final isSelected = reciter.id == settingsProvider.selectedReciterId;

              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: isSelected ? Colors.green : Colors.grey.shade300,
                  child: Icon(
                    isSelected ? Icons.check : Icons.person,
                    color: isSelected ? Colors.white : Colors.grey.shade600,
                  ),
                ),
                title: Text(
                  reciter.arabicName,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? Colors.green : null,
                  ),
                ),
                subtitle: Text(
                  reciter.style,
                  style: TextStyle(
                    color: isSelected ? Colors.green.withValues(alpha: 0.7) : Colors.grey.shade600,
                  ),
                ),
                trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Colors.green)
                  : null,
                onTap: () {
                  settingsProvider.setSelectedReciter(reciter.id);
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم اختيار ${reciter.arabicName} كقارئ مفضل'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showTimePicker(
    BuildContext context,
    TimeOfDay initialTime,
    Function(TimeOfDay) onTimeSelected,
  ) async {
    final time = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (time != null) {
      onTimeSelected(time);
    }
  }

  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<SettingsProvider>(context, listen: false).resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إعادة تعيين الإعدادات بنجاح'),
                ),
              );
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }
}
