# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # Production-ready rules for Google Play Store
    avoid_print: false  # Allow for debugging, will be removed by ProGuard
    deprecated_member_use_from_same_package: false  # Allow deprecated APIs temporarily
    # Enable good practices
    prefer_single_quotes: true
    prefer_const_constructors: true
    prefer_const_literals_to_create_immutables: true
    # Enable additional quality rules
    avoid_unnecessary_containers: true
    avoid_empty_else: true
    avoid_returning_null_for_void: true
    prefer_is_empty: true
    prefer_is_not_empty: true
    use_key_in_widget_constructors: true
    sized_box_for_whitespace: true
    avoid_web_libraries_in_flutter: true

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.mocks.dart"
  errors:
    # Treat as info for Google Play Store compliance
    deprecated_member_use: info
    unused_import: info
    avoid_print: info
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
