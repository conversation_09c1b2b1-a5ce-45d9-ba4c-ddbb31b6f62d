import 'dart:io';
import 'package:http/http.dart' as http;

/// Enhanced API error handler for better error management
class ApiErrorHandler {
  /// Handle HTTP response errors
  static String handleHttpError(http.Response response) {
    switch (response.statusCode) {
      case 400:
        return 'طلب غير صحيح - يرجى المحاولة مرة أخرى';
      case 401:
        return 'غير مصرح - يرجى التحقق من الإعدادات';
      case 403:
        return 'ممنوع - ليس لديك صلاحية للوصول';
      case 404:
        return 'البيانات غير موجودة';
      case 429:
        return 'تم تجاوز حد الطلبات - يرجى المحاولة لاحقاً';
      case 500:
        return 'خطأ في الخادم - يرجى المحاولة لاحقاً';
      case 502:
        return 'خطأ في البوابة - يرجى المحاولة لاحقاً';
      case 503:
        return 'الخدمة غير متاحة مؤقتاً';
      default:
        return 'حدث خطأ غير متوقع (${response.statusCode})';
    }
  }

  /// Handle network exceptions
  static String handleNetworkError(dynamic error) {
    if (error is SocketException) {
      return 'لا يوجد اتصال بالإنترنت - يرجى التحقق من الاتصال';
    } else if (error is HttpException) {
      return 'خطأ في الشبكة - يرجى المحاولة مرة أخرى';
    } else if (error is FormatException) {
      return 'خطأ في تنسيق البيانات';
    } else if (error.toString().contains('timeout')) {
      return 'انتهت مهلة الاتصال - يرجى المحاولة مرة أخرى';
    } else {
      return 'حدث خطأ غير متوقع - يرجى المحاولة مرة أخرى';
    }
  }

  /// Check if error is retryable
  static bool isRetryableError(dynamic error) {
    if (error is SocketException) return true;
    if (error is HttpException) return true;
    if (error.toString().contains('timeout')) return true;
    
    if (error is http.Response) {
      return error.statusCode >= 500 || error.statusCode == 429;
    }
    
    return false;
  }

  /// Get retry delay based on attempt number
  static Duration getRetryDelay(int attempt) {
    // Exponential backoff: 1s, 2s, 4s, 8s...
    return Duration(seconds: (1 << attempt).clamp(1, 30));
  }

  /// Log error for debugging
  static void logError(String context, dynamic error, [StackTrace? stackTrace]) {
    print('API Error in $context: $error');
    if (stackTrace != null) {
      print('Stack trace: $stackTrace');
    }
  }

  /// Create user-friendly error message
  static String createUserMessage(String context, dynamic error) {
    String baseMessage = handleNetworkError(error);
    
    switch (context.toLowerCase()) {
      case 'quran':
        return 'خطأ في تحميل القرآن الكريم: $baseMessage';
      case 'hadith':
        return 'خطأ في تحميل الأحاديث: $baseMessage';
      case 'prayer':
        return 'خطأ في تحميل أوقات الصلاة: $baseMessage';
      case 'azkar':
        return 'خطأ في تحميل الأذكار: $baseMessage';
      default:
        return baseMessage;
    }
  }
}

/// API retry mechanism
class ApiRetryHelper {
  /// Execute API call with retry logic
  static Future<T?> executeWithRetry<T>(
    Future<T> Function() apiCall,
    String context, {
    int maxRetries = 3,
    Duration? initialDelay,
  }) async {
    int attempt = 0;
    
    while (attempt < maxRetries) {
      try {
        return await apiCall();
      } catch (error) {
        attempt++;
        
        if (attempt >= maxRetries || !ApiErrorHandler.isRetryableError(error)) {
          ApiErrorHandler.logError(context, error);
          rethrow;
        }
        
        final delay = initialDelay ?? ApiErrorHandler.getRetryDelay(attempt - 1);
        await Future.delayed(delay);
      }
    }
    
    return null;
  }
}

/// Network connectivity checker
class NetworkChecker {
  /// Check if device has internet connection
  static Future<bool> hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Check if specific API endpoint is reachable
  static Future<bool> isApiReachable(String url) async {
    try {
      final response = await http.head(Uri.parse(url))
          .timeout(const Duration(seconds: 5));
      return response.statusCode < 400;
    } catch (e) {
      return false;
    }
  }
}
