import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';

class QuickSettingsWidget extends StatelessWidget {
  const QuickSettingsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.tune,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'الإعدادات السريعة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildQuickSetting(
                      context,
                      icon: settings.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                      label: settings.isDarkMode ? 'النهاري' : 'الليلي',
                      onTap: () => _toggleDarkMode(context),
                      isActive: settings.isDarkMode,
                    ),
                    _buildQuickSetting(
                      context,
                      icon: Icons.text_fields,
                      label: 'الخط',
                      onTap: () => _showFontSizeDialog(context),
                      isActive: false,
                    ),
                    _buildQuickSetting(
                      context,
                      icon: Icons.volume_up,
                      label: 'الصوت',
                      onTap: () => _showAudioSettings(context),
                      isActive: false,
                    ),
                    _buildQuickSetting(
                      context,
                      icon: Icons.notifications,
                      label: 'التنبيهات',
                      onTap: () => _showNotificationSettings(context),
                      isActive: settings.notificationsEnabled == true,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _toggleDarkMode(BuildContext context) {
    final settings = Provider.of<SettingsProvider>(context, listen: false);
    // استخدام method موجود أو إنشاء واحد بسيط
    try {
      settings.setDarkMode(!settings.isDarkMode);
    } catch (e) {
      // fallback - يمكن إضافة snackbar للإشعار
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('سيتم إضافة هذه الميزة قريباً')),
      );
    }
  }

  Widget _buildQuickSetting(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required bool isActive,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: isActive
              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
              : Colors.transparent,
          border: isActive
              ? Border.all(color: Theme.of(context).primaryColor.withValues(alpha: 0.3))
              : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 24,
              color: isActive 
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).iconTheme.color,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isActive 
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).textTheme.bodyMedium?.color,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const FontSizeDialog(),
    );
  }

  void _showAudioSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => const AudioSettingsSheet(),
    );
  }

  void _showNotificationSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => const NotificationSettingsSheet(),
    );
  }
}

class FontSizeDialog extends StatefulWidget {
  const FontSizeDialog({super.key});

  @override
  State<FontSizeDialog> createState() => _FontSizeDialogState();
}

class _FontSizeDialogState extends State<FontSizeDialog> {
  double _fontSize = 16.0;

  @override
  void initState() {
    super.initState();
    final settings = Provider.of<SettingsProvider>(context, listen: false);
    _fontSize = settings.fontSize;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('حجم الخط'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'مثال على النص',
            style: TextStyle(fontSize: _fontSize),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              const Text('صغير'),
              Expanded(
                child: Slider(
                  value: _fontSize,
                  min: 12.0,
                  max: 24.0,
                  divisions: 12,
                  onChanged: (value) {
                    setState(() {
                      _fontSize = value;
                    });
                  },
                ),
              ),
              const Text('كبير'),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            final settings = Provider.of<SettingsProvider>(context, listen: false);
            settings.setFontSize(_fontSize);
            Navigator.pop(context);
          },
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}

class AudioSettingsSheet extends StatefulWidget {
  const AudioSettingsSheet({super.key});

  @override
  State<AudioSettingsSheet> createState() => _AudioSettingsSheetState();
}

class _AudioSettingsSheetState extends State<AudioSettingsSheet> {
  double _volume = 0.8;
  double _playbackSpeed = 1.0;
  bool _autoPlayNext = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات الصوت',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          // مستوى الصوت
          const Text('مستوى الصوت'),
          Row(
            children: [
              const Icon(Icons.volume_down),
              Expanded(
                child: Slider(
                  value: _volume,
                  onChanged: (value) {
                    setState(() {
                      _volume = value;
                    });
                  },
                ),
              ),
              const Icon(Icons.volume_up),
            ],
          ),
          Text('${(_volume * 100).toInt()}%'),
          
          // سرعة التشغيل
          const SizedBox(height: 16),
          const Text('سرعة التشغيل'),
          Row(
            children: [
              const Text('0.5x'),
              Expanded(
                child: Slider(
                  value: _playbackSpeed,
                  min: 0.5,
                  max: 2.0,
                  divisions: 6,
                  onChanged: (value) {
                    setState(() {
                      _playbackSpeed = value;
                    });
                  },
                ),
              ),
              const Text('2.0x'),
            ],
          ),
          Text('${_playbackSpeed.toStringAsFixed(1)}x'),
          
          // التشغيل التلقائي
          SwitchListTile(
            title: const Text('التشغيل التلقائي للسورة التالية'),
            value: _autoPlayNext,
            onChanged: (value) {
              setState(() {
                _autoPlayNext = value;
              });
            },
          ),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

class NotificationSettingsSheet extends StatefulWidget {
  const NotificationSettingsSheet({super.key});

  @override
  State<NotificationSettingsSheet> createState() => _NotificationSettingsSheetState();
}

class _NotificationSettingsSheetState extends State<NotificationSettingsSheet> {
  bool _notificationsEnabled = true;
  bool _prayerNotifications = true;
  bool _readingReminders = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات التنبيهات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          SwitchListTile(
            title: const Text('تفعيل التنبيهات'),
            subtitle: const Text('تلقي تنبيهات للقراءة والأذكار'),
            value: _notificationsEnabled,
            onChanged: (value) {
              setState(() {
                _notificationsEnabled = value;
              });
            },
          ),
          
          SwitchListTile(
            title: const Text('تنبيهات أوقات الصلاة'),
            subtitle: const Text('تذكير بأوقات الصلاة'),
            value: _prayerNotifications,
            onChanged: _notificationsEnabled
                ? (value) {
                    setState(() {
                      _prayerNotifications = value;
                    });
                  }
                : null,
          ),
          
          SwitchListTile(
            title: const Text('تنبيهات القراءة اليومية'),
            subtitle: const Text('تذكير يومي لقراءة القرآن'),
            value: _readingReminders,
            onChanged: _notificationsEnabled
                ? (value) {
                    setState(() {
                      _readingReminders = value;
                    });
                  }
                : null,
          ),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}