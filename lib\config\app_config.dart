import 'package:flutter/foundation.dart';
import '../utils/app_constants.dart';

/// Application configuration and environment settings
class AppConfig {
  static const String _prodApiUrl = 'https://api.alquran.cloud/v1';
  static const String _devApiUrl = 'https://api.alquran.cloud/v1';
  
  /// Get API base URL based on environment
  static String get apiBaseUrl => kDebugMode ? _devApiUrl : _prodApiUrl;
  
  /// App environment
  static bool get isProduction => kReleaseMode;
  static bool get isDevelopment => kDebugMode;
  
  /// Feature flags
  static bool get enableAnalytics => AppConstants.enableAnalytics && isProduction;
  static bool get enableCrashReporting => AppConstants.enableCrashReporting;
  static bool get enableLogging => isDevelopment;
  
  /// API Configuration
  static const Duration apiTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  /// Cache Configuration
  static const Duration cacheExpiry = Duration(hours: 24);
  static const int maxCacheSize = 50; // MB
  
  /// Database Configuration
  static const String dbName = AppConstants.databaseName;
  static const int dbVersion = AppConstants.databaseVersion;
  
  /// Notification Configuration
  static const Map<String, String> notificationChannels = {
    'prayer_times': 'أوقات الصلاة',
    'azkar_reminder': 'تذكير الأذكار',
    'hadith_daily': 'حديث اليوم',
    'quran_reading': 'تذكير قراءة القرآن',
  };
  
  /// Audio Configuration
  static const List<String> supportedAudioFormats = ['mp3', 'wav', 'aac'];
  static const double defaultVolume = 0.8;
  static const double maxVolume = 1.0;
  static const double minVolume = 0.0;
  
  /// UI Configuration
  static const double minFontSize = AppConstants.minFontSize;
  static const double maxFontSize = AppConstants.maxFontSize;
  static const double defaultFontSize = AppConstants.defaultFontSize;
  
  /// Security Configuration
  static const bool enforceHttps = true;
  static const bool validateCertificates = true;
  static const Duration sessionTimeout = Duration(hours: 24);
  
  /// Performance Configuration
  static const int maxConcurrentRequests = 5;
  static const Duration requestTimeout = Duration(seconds: 15);
  static const int imageMemoryCacheSize = 100; // MB
  
  /// Localization Configuration
  static const String defaultLocale = 'ar';
  static const List<String> supportedLocales = ['ar', 'en'];
  
  /// App Store Configuration
  static const String appStoreId = 'com.islamicapp.quraan';
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.islamicapp.quraan';
  static const String appStoreUrl = 'https://apps.apple.com/app/id123456789';
  
  /// Social and Support
  static const String supportEmail = '<EMAIL>';
  static const String websiteUrl = 'https://islamicapp.com';
  static const String privacyPolicyUrl = 'https://islamicapp.com/privacy';
  static const String termsOfServiceUrl = 'https://islamicapp.com/terms';
  
  /// Development Configuration
  static const bool showPerformanceOverlay = false;
  static const bool showDebugBanner = false;
  static const bool enableInspector = kDebugMode;
  
  /// Validation Rules
  static const int maxBookmarkNoteLength = 500;
  static const int maxTasbihCount = 9999;
  static const int minPasswordLength = 8;
  
  /// Rate Limiting
  static const Map<String, int> rateLimits = {
    'api_calls': 100, // per minute
    'search_requests': 20, // per minute
    'bookmark_operations': 50, // per minute
  };
  
  /// Error Handling
  static const int maxErrorRetries = 3;
  static const Duration errorRetryDelay = Duration(seconds: 1);
  static const bool showDetailedErrors = kDebugMode;
  
  /// Backup and Sync
  static const bool enableAutoBackup = false;
  static const Duration backupInterval = Duration(days: 7);
  static const int maxBackupFiles = 5;
  
  /// Accessibility
  static const bool enableAccessibility = true;
  static const double minTouchTargetSize = 44.0;
  static const bool enableHighContrast = false;
  
  /// Analytics Events (if enabled)
  static const Map<String, String> analyticsEvents = {
    'app_open': 'app_opened',
    'quran_read': 'quran_reading_started',
    'azkar_completed': 'azkar_session_completed',
    'hadith_shared': 'hadith_shared',
    'bookmark_added': 'bookmark_created',
  };
  
  /// Get configuration summary
  static Map<String, dynamic> getConfigSummary() {
    return {
      'environment': isProduction ? 'production' : 'development',
      'api_url': apiBaseUrl,
      'features': {
        'analytics': enableAnalytics,
        'crash_reporting': enableCrashReporting,
        'logging': enableLogging,
      },
      'version': AppConstants.appVersion,
      'build_mode': kDebugMode ? 'debug' : 'release',
    };
  }
  
  /// Validate configuration
  static bool validateConfig() {
    try {
      // Validate required configurations
      assert(apiBaseUrl.isNotEmpty, 'API URL cannot be empty');
      assert(dbName.isNotEmpty, 'Database name cannot be empty');
      assert(supportedLocales.isNotEmpty, 'Supported locales cannot be empty');
      assert(minFontSize < maxFontSize, 'Min font size must be less than max');
      assert(defaultFontSize >= minFontSize && defaultFontSize <= maxFontSize, 
             'Default font size must be within min/max range');
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Configuration validation failed: $e');
      }
      return false;
    }
  }
  
  /// Get environment-specific settings
  static Map<String, dynamic> getEnvironmentSettings() {
    if (isProduction) {
      return {
        'logging_level': 'error',
        'enable_debug_tools': false,
        'api_timeout': 30,
        'cache_duration': 24 * 60 * 60, // 24 hours in seconds
      };
    } else {
      return {
        'logging_level': 'debug',
        'enable_debug_tools': true,
        'api_timeout': 60,
        'cache_duration': 60 * 60, // 1 hour in seconds
      };
    }
  }
}