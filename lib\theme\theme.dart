import 'package:flutter/material.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      colorScheme: const ColorScheme.light(
        primary: Color(0xFF6A1B9A),
        primaryContainer: Color(0xFF9C27B0),
        secondary: Color(0xFF26A69A),
        secondaryContainer: Color(0xFF80CBC4),
        surface: Colors.white,
      ),
      appBarTheme: const AppBarTheme(
        color: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.white),
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
      ),
      textTheme: const TextTheme(
        titleLarge: TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        bodyMedium: TextStyle(
          color: Colors.white70,
          fontSize: 14,
        ),
      ),
      iconTheme: const IconThemeData(color: Colors.white),
      cardTheme: CardTheme(
        color: Colors.white.withAlpha((255 * 0.1).round()),
        elevation: 8,
        shadowColor: Colors.purpleAccent.withAlpha((255 * 0.3).round()),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      buttonTheme: ButtonThemeData(
        buttonColor: const Color(0xFF26A69A),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      colorScheme: const ColorScheme.dark(
        primary: Color(0xFF9C27B0),
        primaryContainer: Color(0xFFBA68C8),
        secondary: Color(0xFF80CBC4),
        secondaryContainer: Color(0xFFB2DFDB),
        surface: Color(0xFF121212),
      ),
      // ... باقي إعدادات الثيم الداكن
    );
  }
}