import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../models/quran_models.dart';
import '../../providers/quran_provider.dart';
import '../../providers/settings_provider.dart';

class JuzReadingScreen extends StatefulWidget {
  final int juzNumber;
  final List<Ayah> verses;

  const JuzReadingScreen({
    super.key,
    required this.juzNumber,
    required this.verses,
  });

  @override
  State<JuzReadingScreen> createState() => _JuzReadingScreenState();
}

class _JuzReadingScreenState extends State<JuzReadingScreen> {
  final ScrollController _scrollController = ScrollController();
  int? _selectedAyahIndex;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('الجزء ${widget.juzNumber}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.text_fields),
            onPressed: _showFontSizeDialog,
          ),
          IconButton(
            icon: const Icon(Icons.bookmark_border),
            onPressed: () {
              Navigator.pushNamed(context, '/quran_bookmarks');
            },
          ),
        ],
      ),
      body: widget.verses.isEmpty
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'لا توجد آيات متاحة',
                    style: TextStyle(fontSize: 18),
                  ),
                ],
              ),
            )
          : Consumer2<QuranProvider, SettingsProvider>(
              builder: (context, quranProvider, settingsProvider, child) {
                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: widget.verses.length,
                  itemBuilder: (context, index) {
                    final ayah = widget.verses[index];
                    final isSelected = _selectedAyahIndex == index;

                    return _buildAyahCard(
                      context,
                      ayah,
                      index,
                      isSelected,
                      settingsProvider.fontSize,
                      quranProvider,
                    );
                  },
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _scrollToTop,
        child: const Icon(Icons.keyboard_arrow_up),
      ),
    );
  }

  Widget _buildAyahCard(
    BuildContext context,
    Ayah ayah,
    int index,
    bool isSelected,
    double fontSize,
    QuranProvider quranProvider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 2,
      color: isSelected
          ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
          : null,
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedAyahIndex = isSelected ? null : index;
          });
        },
        onLongPress: () {
          _showAyahOptions(context, ayah, quranProvider);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Ayah header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${ayah.numberInSurah}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'الآية ${ayah.numberInSurah}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    'صفحة ${ayah.page}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Ayah text
              Text(
                ayah.text,
                style: TextStyle(
                  fontSize: fontSize,
                  height: 1.8,
                  fontFamily: 'Amiri',
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),

              // Action buttons (shown when selected)
              if (isSelected) ...[
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      context,
                      Icons.play_arrow,
                      'تشغيل',
                      () => _playAyah(ayah, quranProvider),
                    ),
                    _buildActionButton(
                      context,
                      Icons.bookmark_border,
                      'حفظ',
                      () => _bookmarkAyah(ayah, quranProvider),
                    ),
                    _buildActionButton(
                      context,
                      Icons.share,
                      'مشاركة',
                      () => _shareAyah(ayah),
                    ),
                    _buildActionButton(
                      context,
                      Icons.copy,
                      'نسخ',
                      () => _copyAyah(ayah),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onPressed,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon),
          style: IconButton.styleFrom(
            backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  void _showAyahOptions(BuildContext context, Ayah ayah, QuranProvider quranProvider) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.play_arrow),
              title: const Text('تشغيل الآية'),
              onTap: () {
                Navigator.pop(context);
                _playAyah(ayah, quranProvider);
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark_border),
              title: const Text('إضافة علامة مرجعية'),
              onTap: () {
                Navigator.pop(context);
                _bookmarkAyah(ayah, quranProvider);
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('مشاركة الآية'),
              onTap: () {
                Navigator.pop(context);
                _shareAyah(ayah);
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('نسخ النص'),
              onTap: () {
                Navigator.pop(context);
                _copyAyah(ayah);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _playAyah(Ayah ayah, QuranProvider quranProvider) {
    // Find the surah that contains this ayah
    final surah = quranProvider.surahs.firstWhere(
      (s) => s.ayahs.any((a) => a.number == ayah.number),
      orElse: () => quranProvider.surahs.first,
    );

    quranProvider.playAyah(surah.number, ayah.numberInSurah);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تشغيل الآية ${ayah.numberInSurah} من سورة ${surah.name}')),
    );
  }

  void _bookmarkAyah(Ayah ayah, QuranProvider quranProvider) async {
    // Find the surah that contains this ayah
    final surah = quranProvider.surahs.firstWhere(
      (s) => s.ayahs.any((a) => a.number == ayah.number),
      orElse: () => quranProvider.surahs.first,
    );

    final isBookmarked = await quranProvider.isBookmarked(surah.number, ayah.numberInSurah);

    if (isBookmarked) {
      await quranProvider.removeBookmark(surah.number, ayah.numberInSurah);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إزالة العلامة المرجعية')),
        );
      }
    } else {
      await quranProvider.addBookmark(surah.number, ayah.numberInSurah);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ العلامة المرجعية')),
        );
      }
    }
  }

  void _shareAyah(Ayah ayah) {
    final shareText = '''${ayah.text}

الآية ${ayah.numberInSurah} - الجزء ${widget.juzNumber}
صفحة ${ayah.page}

من تطبيق قرآني 📱''';

    Share.share(shareText);
  }

  void _copyAyah(Ayah ayah) async {
    final copyText = '''${ayah.text}

الآية ${ayah.numberInSurah} - الجزء ${widget.juzNumber}
صفحة ${ayah.page}''';

    await Clipboard.setData(ClipboardData(text: copyText));

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم نسخ النص إلى الحافظة')),
      );
    }
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return AlertDialog(
            title: const Text('حجم الخط'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'حجم الخط الحالي: ${settingsProvider.fontSize.toInt()}',
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: settingsProvider.fontSize,
                  min: 14,
                  max: 32,
                  divisions: 9,
                  label: settingsProvider.fontSize.toInt().toString(),
                  onChanged: (value) {
                    settingsProvider.setFontSize(value);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }
}
