import 'package:flutter/material.dart';
import '../../services/audio_service.dart';
import '../../models/quran_models.dart';

class ReciterSelectionScreen extends StatefulWidget {
  final Surah? surah;
  final Ayah? ayah;

  const ReciterSelectionScreen({
    super.key,
    this.surah,
    this.ayah,
  });

  @override
  State<ReciterSelectionScreen> createState() => _ReciterSelectionScreenState();
}

class _ReciterSelectionScreenState extends State<ReciterSelectionScreen> {
  List<Reciter> _reciters = [];
  Reciter? _selectedReciter;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadReciters();
  }

  void _loadReciters() {
    setState(() {
      _reciters = AudioService.getDefaultReciters();
      _selectedReciter = _reciters.isNotEmpty ? _reciters.first : null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختيار القارئ'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Header info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: Column(
              children: [
                if (widget.surah != null) ...[
                  Text(
                    'تشغيل ${widget.ayah != null ? 'الآية ${widget.ayah!.numberInSurah} من ' : ''}سورة ${widget.surah!.name}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.surah!.englishNameTranslation,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),

          // Featured reciters section
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: Colors.green,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'القراء المشهورين',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height: 100,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _reciters.take(5).length,
                    itemBuilder: (context, index) {
                      final reciter = _reciters[index];
                      final isSelected = _selectedReciter?.id == reciter.id;

                      return Container(
                        width: 120,
                        margin: const EdgeInsets.only(left: 8),
                        child: Card(
                          elevation: isSelected ? 8 : 2,
                          color: isSelected
                            ? Colors.green.withValues(alpha: 0.1)
                            : null,
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                _selectedReciter = reciter;
                              });
                            },
                            borderRadius: BorderRadius.circular(8),
                            child: Padding(
                              padding: const EdgeInsets.all(8),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CircleAvatar(
                                    radius: 20,
                                    backgroundColor: isSelected
                                      ? Colors.green
                                      : Colors.grey.shade300,
                                    child: Icon(
                                      isSelected ? Icons.check : Icons.person,
                                      color: isSelected ? Colors.white : Colors.grey.shade600,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    reciter.arabicName,
                                    style: TextStyle(
                                      fontSize: 11,
                                      fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                                      color: isSelected ? Colors.green : null,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          // All reciters section header
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(
                  Icons.list,
                  color: Colors.green,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'جميع القراء',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Reciters list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _reciters.length,
              itemBuilder: (context, index) {
                final reciter = _reciters[index];
                final isSelected = _selectedReciter?.id == reciter.id;

                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  elevation: isSelected ? 4 : 1,
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: isSelected ? Colors.green : Colors.grey.shade300,
                      child: Icon(
                        Icons.person,
                        color: isSelected ? Colors.white : Colors.grey.shade600,
                      ),
                    ),
                    title: Text(
                      reciter.arabicName,
                      style: TextStyle(
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected ? Colors.green : null,
                      ),
                    ),
                    subtitle: Text(
                      reciter.style,
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                    ),
                    trailing: isSelected
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(Icons.radio_button_unchecked, color: Colors.grey),
                    onTap: () {
                      setState(() {
                        _selectedReciter = reciter;
                      });
                    },
                  ),
                );
              },
            ),
          ),

          // Action buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _selectedReciter != null && !_isLoading
                        ? _startPlayback
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('تشغيل'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startPlayback() async {
    if (_selectedReciter == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final audioService = AudioService.instance;

      if (widget.ayah != null && widget.surah != null) {
        // Play specific ayah
        await audioService.playAyah(
          widget.surah!.number,
          widget.ayah!.numberInSurah,
          _selectedReciter!,
        );
      } else if (widget.surah != null) {
        // Play full surah
        await audioService.playSurah(
          widget.surah!.number,
          _selectedReciter!,
        );
      }

      if (mounted) {
        Navigator.pop(context);
        _showAudioPlayerDialog();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تشغيل الصوت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showAudioPlayerDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AudioPlayerDialog(
        reciter: _selectedReciter!,
        surah: widget.surah,
        ayah: widget.ayah,
      ),
    );
  }
}

class AudioPlayerDialog extends StatefulWidget {
  final Reciter reciter;
  final Surah? surah;
  final Ayah? ayah;

  const AudioPlayerDialog({
    super.key,
    required this.reciter,
    this.surah,
    this.ayah,
  });

  @override
  State<AudioPlayerDialog> createState() => _AudioPlayerDialogState();
}

class _AudioPlayerDialogState extends State<AudioPlayerDialog> {
  final AudioService _audioService = AudioService.instance;
  bool _isPlaying = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  @override
  void initState() {
    super.initState();
    _setupListeners();
  }

  void _setupListeners() {
    _audioService.onPlayingStateChanged = (isPlaying) {
      if (mounted) {
        setState(() {
          _isPlaying = isPlaying;
        });
      }
    };

    _audioService.onPositionChanged = (position) {
      if (mounted) {
        setState(() {
          _currentPosition = position;
        });
      }
    };

    _audioService.onDurationChanged = (duration) {
      if (mounted) {
        setState(() {
          _totalDuration = duration;
        });
      }
    };

    _audioService.onCompleted = () {
      if (mounted) {
        Navigator.pop(context);
      }
    };
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Text(
              widget.ayah != null
                  ? 'الآية ${widget.ayah!.numberInSurah}'
                  : 'سورة ${widget.surah?.name ?? ''}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.reciter.arabicName,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 20),

            // Progress bar
            Column(
              children: [
                Slider(
                  value: _totalDuration.inMilliseconds > 0
                      ? _currentPosition.inMilliseconds / _totalDuration.inMilliseconds
                      : 0.0,
                  onChanged: (value) {
                    final position = Duration(
                      milliseconds: (value * _totalDuration.inMilliseconds).round(),
                    );
                    _audioService.seek(position);
                  },
                  activeColor: Colors.green,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(_formatDuration(_currentPosition)),
                    Text(_formatDuration(_totalDuration)),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Control buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  onPressed: () async {
                    final navigator = Navigator.of(context);
                    await _audioService.stop();
                    if (mounted) {
                      navigator.pop();
                    }
                  },
                  icon: const Icon(Icons.stop),
                  iconSize: 32,
                ),
                IconButton(
                  onPressed: _isPlaying
                      ? () => _audioService.pause()
                      : () => _audioService.resume(),
                  icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                  iconSize: 48,
                  color: Colors.green,
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                  iconSize: 32,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    _audioService.onPlayingStateChanged = null;
    _audioService.onPositionChanged = null;
    _audioService.onDurationChanged = null;
    _audioService.onCompleted = null;
    super.dispose();
  }
}
