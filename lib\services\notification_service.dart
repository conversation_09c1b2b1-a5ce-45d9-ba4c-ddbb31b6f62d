import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/hadith_models.dart';

class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance => _instance ??= NotificationService._();
  NotificationService._();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    // Request permissions
    await _requestPermissions();

    // Initialize the plugin
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  Future<void> _requestPermissions() async {
    // Request notification permission
    await Permission.notification.request();

    // For Android 13+ (API level 33+), request POST_NOTIFICATIONS permission
    if (await Permission.notification.isDenied) {
      await Permission.notification.request();
    }
  }

  void _onNotificationTapped(NotificationResponse notificationResponse) {
    final payload = notificationResponse.payload;
    if (payload != null) {
      // Handle notification tap based on payload
      _handleNotificationPayload(payload);
    }
  }

  void _handleNotificationPayload(String payload) {
    // Parse payload and navigate to appropriate screen
    final parts = payload.split('|');
    if (parts.isNotEmpty) {
      final type = parts[0];
      switch (type) {
        case 'azkar_reminder':
          // Navigate to Azkar screen
          break;
        case 'hadith_daily':
          // Navigate to Hadith screen
          break;
        case 'quran_reminder':
          // Navigate to Quran screen
          break;
        case 'prayer_reminder':
          // Navigate to Prayer times or Azkar after prayer
          break;
      }
    }
  }

  // Schedule daily Azkar reminders
  Future<void> scheduleAzkarReminders({
    required TimeOfDay morningTime,
    required TimeOfDay eveningTime,
    bool enableMorning = true,
    bool enableEvening = true,
  }) async {
    await _cancelNotificationsByType('azkar');

    if (enableMorning) {
      await _scheduleDailyNotification(
        id: 1,
        title: 'أذكار الصباح',
        body: 'حان وقت أذكار الصباح. ابدأ يومك بذكر الله',
        time: morningTime,
        payload: 'azkar_reminder|morning',
      );
    }

    if (enableEvening) {
      await _scheduleDailyNotification(
        id: 2,
        title: 'أذكار المساء',
        body: 'حان وقت أذكار المساء. اختتم يومك بذكر الله',
        time: eveningTime,
        payload: 'azkar_reminder|evening',
      );
    }
  }

  // Schedule daily Hadith notification
  Future<void> scheduleHadithOfTheDay({
    required TimeOfDay time,
    bool enabled = true,
  }) async {
    await _cancelNotificationsByType('hadith');

    if (enabled) {
      await _scheduleDailyNotification(
        id: 3,
        title: 'حديث اليوم',
        body: 'اقرأ حديث اليوم وتعلم من سنة النبي ﷺ',
        time: time,
        payload: 'hadith_daily',
      );
    }
  }

  // Schedule Quran reading reminder
  Future<void> scheduleQuranReminder({
    required TimeOfDay time,
    bool enabled = true,
    String customMessage = 'حان وقت قراءة القرآن الكريم',
  }) async {
    await _cancelNotificationsByType('quran');

    if (enabled) {
      await _scheduleDailyNotification(
        id: 4,
        title: 'تذكير قراءة القرآن',
        body: customMessage,
        time: time,
        payload: 'quran_reminder',
      );
    }
  }

  // Schedule prayer time reminders
  Future<void> schedulePrayerReminders({
    required List<TimeOfDay> prayerTimes,
    bool enabled = true,
  }) async {
    await _cancelNotificationsByType('prayer');

    if (!enabled) return;

    final prayerNames = ['الفجر', 'الظهر', 'العصر', 'المغرب', 'العشاء'];

    for (int i = 0; i < prayerTimes.length && i < prayerNames.length; i++) {
      await _scheduleDailyNotification(
        id: 10 + i,
        title: 'حان وقت صلاة ${prayerNames[i]}',
        body: 'لا تنس أذكار ما بعد الصلاة',
        time: prayerTimes[i],
        payload: 'prayer_reminder|${prayerNames[i]}',
      );
    }
  }

  Future<void> _scheduleDailyNotification({
    required int id,
    required String title,
    required String body,
    required TimeOfDay time,
    String? payload,
  }) async {
    final now = DateTime.now();
    var scheduledDate = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    // If the time has already passed today, schedule for tomorrow
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'quraan_reminders',
      'تذكيرات قرآني',
      channelDescription: 'تذكيرات للأذكار والقرآن والأحاديث',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      sound: RawResourceAndroidNotificationSound('notification_sound'),
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      sound: 'notification_sound.aiff',
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      _convertToTZDateTime(scheduledDate),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      matchDateTimeComponents: DateTimeComponents.time,
      payload: payload,
    );
  }

  // Show immediate notification for Hadith of the Day
  Future<void> showHadithOfTheDayNotification(Hadith hadith) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'hadith_daily',
      'حديث اليوم',
      channelDescription: 'إشعار يومي بحديث شريف',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      styleInformation: BigTextStyleInformation(''),
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      100,
      'حديث اليوم',
      _truncateText(hadith.arabicText, 100),
      platformChannelSpecifics,
      payload: 'hadith_daily|${hadith.id}',
    );
  }

  // Show completion notification for Azkar
  Future<void> showAzkarCompletionNotification(String azkarName) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'azkar_completion',
      'إتمام الأذكار',
      channelDescription: 'إشعار عند إتمام الأذكار',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      200,
      'أحسنت! 🎉',
      'لقد أتممت $azkarName. جعله الله في ميزان حسناتك',
      platformChannelSpecifics,
    );
  }

  // Cancel notifications by type
  Future<void> _cancelNotificationsByType(String type) async {
    final pendingNotifications = await _flutterLocalNotificationsPlugin.pendingNotificationRequests();

    for (final notification in pendingNotifications) {
      if (notification.payload?.startsWith(type) == true) {
        await _flutterLocalNotificationsPlugin.cancel(notification.id);
      }
    }
  }

  // Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  // Get pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  }

  // Helper method to convert DateTime to TZDateTime
  dynamic _convertToTZDateTime(DateTime dateTime) {
    // This is a simplified version. In a real app, you'd use timezone package
    return dateTime;
  }

  // Helper method to truncate text
  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  // Test notification
  Future<void> showTestNotification() async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'test_channel',
      'اختبار الإشعارات',
      channelDescription: 'قناة اختبار الإشعارات',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      999,
      'اختبار الإشعارات',
      'تم تفعيل الإشعارات بنجاح! 🎉',
      platformChannelSpecifics,
    );
  }
}
