import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';
import '../screens/quran/juz_reading_screen.dart';

class JuzListItem extends StatelessWidget {
  final int juzNumber;

  const JuzListItem({
    super.key,
    required this.juzNumber,
  });

  @override
  Widget build(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '$juzNumber',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        title: Text(
          'الجزء $juzNumber',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          'الحزب ${(juzNumber - 1) * 2 + 1} - ${juzNumber * 2}',
          style: TextStyle(
            color: Colors.grey[600],
          ),
        ),
        trailing: quranProvider.isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.arrow_forward_ios),
        onTap: () async {
          final quranProvider = Provider.of<QuranProvider>(context, listen: false);
          // Load juz verses
          final verses = await quranProvider.getJuzVerses(juzNumber);
          if (verses.isNotEmpty && context.mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => JuzReadingScreen(
                  juzNumber: juzNumber,
                  verses: verses,
                ),
              ),
            );
          } else if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في تحميل الجزء'),
              ),
            );
          }
        },
      ),
    );
  }
}