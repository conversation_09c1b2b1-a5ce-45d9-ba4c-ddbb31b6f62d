import 'package:flutter/material.dart';
import '../models/quran_models.dart';
import '../services/database_service.dart';
import '../services/audio_service.dart';
import '../services/quran_api_service.dart';

class QuranProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService.instance;
  final AudioService _audioService = AudioService.instance;
  final QuranApiService _quranApiService = QuranApiService();

  List<Surah> _surahs = [];
  List<Reciter> _reciters = [];
  List<BookmarkModel> _bookmarks = [];

  Surah? _currentSurah;
  Ayah? _currentAyah;
  Reciter? _currentReciter;

  bool _isLoading = false;
  bool _isPlaying = false;
  String? _errorMessage;

  // Reading progress
  int _lastReadSurah = 1;
  int _lastReadAyah = 1;
  int _currentMushafPage = 1;

  // Getters
  List<Surah> get surahs => _surahs;
  List<Reciter> get reciters => _reciters;
  List<BookmarkModel> get bookmarks => _bookmarks;
  Surah? get currentSurah => _currentSurah;
  Ayah? get currentAyah => _currentAyah;
  Reciter? get currentReciter => _currentReciter;
  bool get isLoading => _isLoading;
  bool get isPlaying => _isPlaying;
  String? get errorMessage => _errorMessage;
  int get lastReadSurah => _lastReadSurah;
  int get lastReadAyah => _lastReadAyah;
  int get lastReadSurahNumber => _lastReadSurah;
  int get lastReadAyahNumber => _lastReadAyah;
  int get currentMushafPage => _currentMushafPage;
  int get lastReadMushafPage => _currentMushafPage;

  Future<void> initialize() async {
    setLoading(true);
    try {
      await _loadSurahs();
      await _loadReciters();
      await _loadBookmarks();
      await _loadReadingProgress();
      _setupAudioCallbacks();
    } catch (e) {
      _errorMessage = 'فشل في تحميل البيانات: $e';
    } finally {
      setLoading(false);
    }
  }

  void _setupAudioCallbacks() {
    _audioService.onPlayingStateChanged = (isPlaying) {
      _isPlaying = isPlaying;
      notifyListeners();
    };
  }

  Future<void> _loadSurahs() async {
    _surahs = await _databaseService.getAllSurahs();

    // If no surahs in database, load from API
    if (_surahs.isEmpty) {
      await _loadQuranFromApi();
    }

    notifyListeners();
  }

  Future<void> _loadReciters() async {
    try {
      // Load reciters from API
      _reciters = await _quranApiService.getReciters();
    } catch (e) {
      // Fallback to default reciters
      _reciters = AudioService.getDefaultReciters();
    }

    _currentReciter = _reciters.isNotEmpty ? _reciters.first : null;
    notifyListeners();
  }

  Future<void> _loadBookmarks() async {
    _bookmarks = await _databaseService.getAllBookmarks();
    notifyListeners();
  }

  Future<void> _loadReadingProgress() async {
    final progress = await _databaseService.getReadingProgress();
    if (progress.isNotEmpty) {
      final latest = progress.first;
      _lastReadSurah = latest['surah_number'] as int;
      _lastReadAyah = latest['ayah_number'] as int;
    }
    notifyListeners();
  }

  Future<void> _loadQuranFromApi() async {
    try {
      // Load surahs from API
      final apiSurahs = await _quranApiService.getAllSurahs();

      if (apiSurahs.isNotEmpty) {
        // Save to database
        for (final surah in apiSurahs) {
          await _databaseService.saveSurah(surah);
        }
        _surahs = apiSurahs;
      } else {
        // Fallback to default data
        await _loadDefaultQuranData();
      }
    } catch (e) {
      // If API fails, use default data
      await _loadDefaultQuranData();
    }
  }

  Future<void> _loadDefaultQuranData() async {
    // Fallback data if API fails
    final defaultSurahs = _createDefaultSurahs();

    for (final surah in defaultSurahs) {
      await _databaseService.saveSurah(surah);
    }

    _surahs = defaultSurahs;
  }

  List<Surah> _createDefaultSurahs() {
    // This is a simplified version with just a few surahs
    // In a real app, you'd load the complete Quran data
    return [
      Surah(
        number: 1,
        name: 'الفاتحة',
        englishName: 'Al-Fatihah',
        englishNameTranslation: 'The Opening',
        revelationType: 'Meccan',
        numberOfAyahs: 7,
        ayahs: [
          Ayah(
            number: 1,
            text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
            numberInSurah: 1,
            juz: 1,
            manzil: 1,
            page: 1,
            ruku: 1,
            hizbQuarter: 1,
            sajda: false,
          ),
          Ayah(
            number: 2,
            text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
            numberInSurah: 2,
            juz: 1,
            manzil: 1,
            page: 1,
            ruku: 1,
            hizbQuarter: 1,
            sajda: false,
          ),
          // Add more ayahs...
        ],
      ),
      Surah(
        number: 2,
        name: 'البقرة',
        englishName: 'Al-Baqarah',
        englishNameTranslation: 'The Cow',
        revelationType: 'Medinan',
        numberOfAyahs: 286,
        ayahs: [
          Ayah(
            number: 1,
            text: 'الم',
            numberInSurah: 1,
            juz: 1,
            manzil: 1,
            page: 2,
            ruku: 1,
            hizbQuarter: 1,
            sajda: false,
          ),
          // Add more ayahs...
        ],
      ),
      // Add more surahs...
    ];
  }

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setCurrentSurah(Surah surah) {
    _currentSurah = surah;
    notifyListeners();
  }

  void setCurrentAyah(Ayah ayah) {
    _currentAyah = ayah;
    // Update reading progress when user views an ayah
    if (_currentSurah != null) {
      saveReadingProgress(_currentSurah!.number, ayah.numberInSurah);
    }
    notifyListeners();
  }

  void setCurrentReciter(Reciter reciter) {
    _currentReciter = reciter;
    notifyListeners();
  }

  Future<void> addBookmark(int surahNumber, int ayahNumber) async {
    final surah = _surahs.firstWhere((s) => s.number == surahNumber);
    final ayah = surah.ayahs.firstWhere((a) => a.numberInSurah == ayahNumber);

    final bookmark = BookmarkModel(
      surahNumber: surahNumber,
      ayahNumber: ayahNumber,
      surahName: surah.name,
      ayahText: ayah.text,
      createdAt: DateTime.now(),
    );

    await _databaseService.addBookmark(bookmark);
    await _loadBookmarks();
  }

  Future<void> removeBookmark(int surahNumber, int ayahNumber) async {
    await _databaseService.removeBookmark(surahNumber, ayahNumber);
    await _loadBookmarks();
  }

  Future<bool> isBookmarked(int surahNumber, int ayahNumber) async {
    return await _databaseService.isBookmarked(surahNumber, ayahNumber);
  }

  Future<void> saveReadingProgress(int surahNumber, int ayahNumber) async {
    _lastReadSurah = surahNumber;
    _lastReadAyah = ayahNumber;

    await _databaseService.saveReadingProgress(
      surahNumber,
      ayahNumber,
      DateTime.now().millisecondsSinceEpoch,
    );

    notifyListeners();
  }

  // Audio methods
  Future<void> playSurah(int surahNumber, {int? startFromAyah}) async {
    if (_currentReciter == null) return;

    try {
      await _audioService.playSurah(
        surahNumber,
        _currentReciter!,
        startFromAyah: startFromAyah,
      );
    } catch (e) {
      _errorMessage = 'فشل في تشغيل الصوت: $e';
      notifyListeners();
    }
  }

  Future<void> playAyah(int surahNumber, int ayahNumber) async {
    if (_currentReciter == null) return;

    try {
      await _audioService.playAyah(surahNumber, ayahNumber, _currentReciter!);
    } catch (e) {
      _errorMessage = 'فشل في تشغيل الآية: $e';
      notifyListeners();
    }
  }

  Future<void> pauseAudio() async {
    await _audioService.pause();
  }

  Future<void> resumeAudio() async {
    await _audioService.resume();
  }

  Future<void> stopAudio() async {
    await _audioService.stop();
  }

  // Search methods
  List<Surah> searchSurahs(String query) {
    if (query.isEmpty) return _surahs;

    return _surahs.where((surah) =>
      surah.name.contains(query) ||
      surah.englishName.toLowerCase().contains(query.toLowerCase()) ||
      surah.englishNameTranslation.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  List<Ayah> searchAyahs(String query) {
    if (query.isEmpty) return [];

    final results = <Ayah>[];
    for (final surah in _surahs) {
      for (final ayah in surah.ayahs) {
        if (ayah.text.contains(query)) {
          results.add(ayah);
        }
      }
    }
    return results;
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Juz (Para) methods
  Future<List<Ayah>> getJuzVerses(int juzNumber) async {
    try {
      setLoading(true);
      final verses = await _quranApiService.getVersesByJuz(juzNumber);
      setLoading(false);
      return verses;
    } catch (e) {
      setLoading(false);
      _errorMessage = 'فشل في تحميل الجزء: $e';
      notifyListeners();
      return [];
    }
  }

  // Page methods
  Future<List<Ayah>> getPageVerses(int pageNumber) async {
    try {
      setLoading(true);
      final verses = await _quranApiService.getVersesByPage(pageNumber);
      setLoading(false);
      return verses;
    } catch (e) {
      setLoading(false);
      _errorMessage = 'فشل في تحميل الصفحة: $e';
      notifyListeners();
      return [];
    }
  }

  // Get Juz list
  Future<List<Map<String, dynamic>>> getJuzList() async {
    try {
      return await _quranApiService.getJuzList();
    } catch (e) {
      _errorMessage = 'فشل في تحميل قائمة الأجزاء: $e';
      notifyListeners();
      return _getDefaultJuzList();
    }
  }

  // Default Juz list if API fails
  List<Map<String, dynamic>> _getDefaultJuzList() {
    return List.generate(30, (index) {
      final juzNumber = index + 1;
      return {
        'juz_number': juzNumber,
        'first_verse_id': (juzNumber - 1) * 200 + 1, // Approximate
        'last_verse_id': juzNumber * 200,
        'verses_count': 200, // Approximate
      };
    });
  }

  // Refresh data from API
  Future<void> refreshQuranData() async {
    try {
      setLoading(true);
      await _loadQuranFromApi();
      await _loadReciters();
      setLoading(false);
    } catch (e) {
      setLoading(false);
      _errorMessage = 'فشل في تحديث البيانات: $e';
      notifyListeners();
    }
  }

  // Get specific surah from API
  Future<Surah?> loadSurah(int surahNumber) async {
    try {
      setLoading(true);
      final surah = await _quranApiService.getSurah(surahNumber);
      setLoading(false);

      if (surah != null) {
        // Update local data
        final index = _surahs.indexWhere((s) => s.number == surahNumber);
        if (index != -1) {
          _surahs[index] = surah;
        } else {
          _surahs.add(surah);
        }
        await _databaseService.saveSurah(surah);
        notifyListeners();
      }

      return surah;
    } catch (e) {
      setLoading(false);
      _errorMessage = 'فشل في تحميل السورة: $e';
      notifyListeners();
      return null;
    }
  }

  // Additional methods for mushaf functionality
  void setCurrentMushafPage(int page) {
    _currentMushafPage = page;
    notifyListeners();
  }

  void setLastReadSurah(int surahNumber, int? ayahNumber) {
    _lastReadSurah = surahNumber;
    if (ayahNumber != null) {
      _lastReadAyah = ayahNumber;
    }
    saveReadingProgress(surahNumber, ayahNumber ?? 1);
    notifyListeners();
  }

  Future<List<Ayah>> getAyahsForPage(int pageNumber) async {
    try {
      return await _quranApiService.getVersesByPage(pageNumber);
    } catch (e) {
      _errorMessage = 'فشل في تحميل آيات الصفحة: $e';
      notifyListeners();
      return [];
    }
  }

  Surah? getSurahInfoForPage(int pageNumber) {
    // Find the first surah that contains ayahs on this page
    for (final surah in _surahs) {
      for (final ayah in surah.ayahs) {
        if (ayah.page == pageNumber) {
          return surah;
        }
      }
    }
    return null;
  }

  int getJuzNumberForPage(int pageNumber) {
    // Find the juz number for the given page
    for (final surah in _surahs) {
      for (final ayah in surah.ayahs) {
        if (ayah.page == pageNumber) {
          return ayah.juz;
        }
      }
    }
    return 1; // Default to juz 1 if not found
  }

  Surah? getSurahOfAyah(int ayahNumber) {
    // Find the surah that contains this ayah
    for (final surah in _surahs) {
      for (final ayah in surah.ayahs) {
        if (ayah.number == ayahNumber) {
          return surah;
        }
      }
    }
    return null;
  }

  Future<List<Map<String, dynamic>>> searchVersesOnline(String query) async {
    try {
      setLoading(true);
      // Try API search first
      try {
        final results = await _quranApiService.searchVerses(query);
        setLoading(false);
        return results;
      } catch (apiError) {
        // Fallback to local search if API fails
        final ayahs = searchAyahs(query);
        setLoading(false);
        return ayahs.map((ayah) => {
          'text': ayah.text,
          'surah_name': getSurahOfAyah(ayah.number)?.name ?? 'غير معروف',
          'ayah_number': ayah.numberInSurah,
          'surah_number': getSurahOfAyah(ayah.number)?.number ?? 1,
        }).toList();
      }
    } catch (e) {
      setLoading(false);
      _errorMessage = 'فشل في البحث: $e';
      notifyListeners();
      return [];
    }
  }
}
