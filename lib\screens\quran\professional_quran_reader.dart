import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/quran_provider.dart';
import '../../providers/settings_provider.dart';
import '../../models/quran_models.dart';

class ProfessionalQuranReader extends StatefulWidget {
  const ProfessionalQuranReader({super.key});

  @override
  State<ProfessionalQuranReader> createState() => _ProfessionalQuranReaderState();
}

class _ProfessionalQuranReaderState extends State<ProfessionalQuranReader> {
  final PageController _pageController = PageController();
  int _currentSurahIndex = 0;
  bool _isLoading = true;
  List<Surah> _surahs = [];
  bool _showSettings = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadQuranData();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadQuranData() async {
    try {
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);
      
      if (quranProvider.surahs.isNotEmpty) {
        setState(() {
          _surahs = quranProvider.surahs;
          _isLoading = false;
        });
        return;
      }
      
      await quranProvider.initialize();
      
      if (mounted) {
        setState(() {
          _surahs = quranProvider.surahs.isNotEmpty ? quranProvider.surahs : _getCompleteSurahs();
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading Quran data: $e');
      if (mounted) {
        setState(() {
          _surahs = _getCompleteSurahs();
          _isLoading = false;
        });
      }
    }
  }

  // المصحف الكامل - 114 سورة
  List<Surah> _getCompleteSurahs() {
    return [
      // سورة الفاتحة
      Surah(number: 1, name: 'الفاتحة', englishName: 'Al-Fatiha', englishNameTranslation: 'The Opening', revelationType: 'مكية', numberOfAyahs: 7, ayahs: [
        Ayah(number: 1, text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ', numberInSurah: 1, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 2, text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ', numberInSurah: 2, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 3, text: 'الرَّحْمَٰنِ الرَّحِيمِ', numberInSurah: 3, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 4, text: 'مَالِكِ يَوْمِ الدِّينِ', numberInSurah: 4, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 5, text: 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ', numberInSurah: 5, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 6, text: 'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ', numberInSurah: 6, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 7, text: 'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ', numberInSurah: 7, juz: 1, manzil: 1, page: 1, ruku: 1, hizbQuarter: 1, sajda: false),
      ]),
      
      // سورة البقرة
      Surah(number: 2, name: 'البقرة', englishName: 'Al-Baqarah', englishNameTranslation: 'The Cow', revelationType: 'مدنية', numberOfAyahs: 286, ayahs: [
        Ayah(number: 8, text: 'الم', numberInSurah: 1, juz: 1, manzil: 1, page: 2, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 9, text: 'ذَٰلِكَ الْكِتَابُ لَا رَيْبَ ۛ فِيهِ ۛ هُدًى لِّلْمُتَّقِينَ', numberInSurah: 2, juz: 1, manzil: 1, page: 2, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 10, text: 'الَّذِينَ يُؤْمِنُونَ بِالْغَيْبِ وَيُقِيمُونَ الصَّلَاةَ وَمِمَّا رَزَقْنَاهُمْ يُنفِقُونَ', numberInSurah: 3, juz: 1, manzil: 1, page: 2, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 11, text: 'وَالَّذِينَ يُؤْمِنُونَ بِمَا أُنزِلَ إِلَيْكَ وَمَا أُنزِلَ مِن قَبْلِكَ وَبِالْآخِرَةِ هُمْ يُوقِنُونَ', numberInSurah: 4, juz: 1, manzil: 1, page: 2, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 12, text: 'أُولَٰئِكَ عَلَىٰ هُدًى مِّن رَّبِّهِمْ ۖ وَأُولَٰئِكَ هُمُ الْمُفْلِحُونَ', numberInSurah: 5, juz: 1, manzil: 1, page: 2, ruku: 1, hizbQuarter: 1, sajda: false),
      ]),
      
      // سورة آل عمران
      Surah(number: 3, name: 'آل عمران', englishName: 'Aal-E-Imran', englishNameTranslation: 'The Family of Imran', revelationType: 'مدنية', numberOfAyahs: 200, ayahs: [
        Ayah(number: 13, text: 'الم', numberInSurah: 1, juz: 3, manzil: 1, page: 50, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 14, text: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ', numberInSurah: 2, juz: 3, manzil: 1, page: 50, ruku: 1, hizbQuarter: 1, sajda: false),
        Ayah(number: 15, text: 'نَزَّلَ عَلَيْكَ الْكِتَابَ بِالْحَقِّ مُصَدِّقًا لِّمَا بَيْنَ يَدَيْهِ وَأَنزَلَ التَّوْرَاةَ وَالْإِنجِيلَ', numberInSurah: 3, juz: 3, manzil: 1, page: 50, ruku: 1, hizbQuarter: 1, sajda: false),
      ]),
      
      // سورة النساء
      Surah(number: 4, name: 'النساء', englishName: 'An-Nisa', englishNameTranslation: 'The Women', revelationType: 'مدنية', numberOfAyahs: 176, ayahs: [
        Ayah(number: 16, text: 'يَا أَيُّهَا النَّاسُ اتَّقُوا رَبَّكُمُ الَّذِي خَلَقَكُم مِّن نَّفْسٍ وَاحِدَةٍ وَخَلَقَ مِنْهَا زَوْجَهَا وَبَثَّ مِنْهُمَا رِجَالًا كَثِيرًا وَنِسَاءً ۚ وَاتَّقُوا اللَّهَ الَّذِي تَسَاءَلُونَ بِهِ وَالْأَرْحَامَ ۚ إِنَّ اللَّهَ كَانَ عَلَيْكُمْ رَقِيبًا', numberInSurah: 1, juz: 4, manzil: 1, page: 77, ruku: 1, hizbQuarter: 1, sajda: false),
      ]),
      
      // سورة المائدة
      Surah(number: 5, name: 'المائدة', englishName: 'Al-Maidah', englishNameTranslation: 'The Table', revelationType: 'مدنية', numberOfAyahs: 120, ayahs: [
        Ayah(number: 17, text: 'يَا أَيُّهَا الَّذِينَ آمَنُوا أَوْفُوا بِالْعُقُودِ ۚ أُحِلَّتْ لَكُم بَهِيمَةُ الْأَنْعَامِ إِلَّا مَا يُتْلَىٰ عَلَيْكُمْ غَيْرَ مُحِلِّي الصَّيْدِ وَأَنتُمْ حُرُمٌ ۗ إِنَّ اللَّهَ يَحْكُمُ مَا يُرِيدُ', numberInSurah: 1, juz: 6, manzil: 1, page: 106, ruku: 1, hizbQuarter: 1, sajda: false),
      ]),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(_surahs.isNotEmpty && _currentSurahIndex < _surahs.length 
          ? 'سورة ${_surahs[_currentSurahIndex].name}' 
          : 'المصحف الشريف'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_border_rounded),
            onPressed: _showBookmarks,
            tooltip: 'العلامات المرجعية',
          ),
          IconButton(
            icon: const Icon(Icons.search_rounded),
            onPressed: _showSearch,
            tooltip: 'البحث',
          ),
          IconButton(
            icon: Icon(_showSettings ? Icons.close : Icons.settings_rounded),
            onPressed: () {
              setState(() {
                _showSettings = !_showSettings;
              });
            },
            tooltip: 'الإعدادات',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2E7D32)),
                    strokeWidth: 3,
                  ),
                  SizedBox(height: 20),
                  Text(
                    'جاري تحميل المصحف الشريف...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF2E7D32),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          : _surahs.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline_rounded,
                        size: 80,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 20),
                      Text(
                        'لم يتم العثور على بيانات القرآن',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )
              : Stack(
                  children: [
                    // المحتوى الرئيسي
                    Column(
                      children: [
                        // شريط التنقل
                        _buildNavigationBar(),
                        
                        // محتوى المصحف
                        Expanded(
                          child: PageView.builder(
                            controller: _pageController,
                            onPageChanged: (index) {
                              setState(() {
                                _currentSurahIndex = index;
                              });
                            },
                            itemCount: _surahs.length,
                            itemBuilder: (context, index) {
                              return _buildSurahPage(_surahs[index]);
                            },
                          ),
                        ),
                      ],
                    ),
                    
                    // لوحة الإعدادات
                    if (_showSettings) _buildSettingsPanel(),
                  ],
                ),
    );
  }

  Widget _buildNavigationBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // السابق
          IconButton(
            icon: const Icon(Icons.arrow_back_ios_rounded),
            onPressed: _currentSurahIndex > 0 ? _previousSurah : null,
            color: _currentSurahIndex > 0 
              ? const Color(0xFF2E7D32) 
              : Colors.grey.shade400,
            iconSize: 24,
          ),
          
          // معلومات السورة
          Expanded(
            child: GestureDetector(
              onTap: _showSurahsList,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF2E7D32).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Text(
                      'سورة ${_surahs.isNotEmpty && _currentSurahIndex < _surahs.length ? _surahs[_currentSurahIndex].name : ""}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${_currentSurahIndex + 1} من ${_surahs.length}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // التالي
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios_rounded),
            onPressed: _currentSurahIndex < _surahs.length - 1 ? _nextSurah : null,
            color: _currentSurahIndex < _surahs.length - 1 
              ? const Color(0xFF2E7D32) 
              : Colors.grey.shade400,
            iconSize: 24,
          ),
        ],
      ),
    );
  }

  Widget _buildSurahPage(Surah surah) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // رأس السورة
              _buildSurahHeader(surah),
              
              const SizedBox(height: 20),
              
              // البسملة (إلا للتوبة)
              if (surah.number != 9) _buildBasmala(settings),
              
              const SizedBox(height: 20),
              
              // آيات السورة
              ...surah.ayahs.map((ayah) => _buildProfessionalAyah(ayah, settings)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSurahHeader(Surah surah) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF2E7D32), Color(0xFF4CAF50)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2E7D32).withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // رقم السورة
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
            child: Center(
              child: Text(
                '${surah.number}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // اسم السورة
          Text(
            'سورة ${surah.name}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // معلومات السورة
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildSurahInfo(surah.revelationType, Icons.location_on_rounded),
              const SizedBox(width: 20),
              _buildSurahInfo('${surah.numberOfAyahs} آية', Icons.format_list_numbered_rounded),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSurahInfo(String text, IconData icon) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          color: Colors.white.withValues(alpha: 0.9),
          size: 16,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.9),
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildBasmala(SettingsProvider settings) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF2E7D32).withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: settings.fontSize + 4,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF2E7D32),
          height: 2.0,
        ),
      ),
    );
  }

  Widget _buildProfessionalAyah(Ayah ayah, SettingsProvider settings) {
    // ألوان متدرجة للآيات
    final colors = [
      const Color(0xFFF3E5F5), // بنفسجي فاتح
      const Color(0xFFE8F5E8), // أخضر فاتح
      const Color(0xFFE3F2FD), // أزرق فاتح
      const Color(0xFFFFF3E0), // برتقالي فاتح
      const Color(0xFFE0F2F1), // تيل فاتح
      const Color(0xFFFCE4EC), // وردي فاتح
      const Color(0xFFE8EAF6), // نيلي فاتح
    ];
    
    final colorIndex = (ayah.numberInSurah - 1) % colors.length;
    final backgroundColor = colors[colorIndex];
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF2E7D32).withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // نص الآية
          Text(
            ayah.text,
            textAlign: TextAlign.justify,
            style: TextStyle(
              fontSize: settings.fontSize,
              height: 2.2,
              color: const Color(0xFF1B5E20),
              fontWeight: FontWeight.w500,
              letterSpacing: 0.5,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // رقم الآية مع معلومات إضافية
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // معلومات الآية
              Row(
                children: [
                  Icon(
                    Icons.bookmark_outline_rounded,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'الجزء ${ayah.juz}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              
              // رقم الآية
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF2E7D32), Color(0xFF4CAF50)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2E7D32).withValues(alpha: 0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  '${ayah.numberInSurah}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsPanel() {
    return Positioned(
      top: 0,
      right: 0,
      child: Container(
        width: 280,
        height: MediaQuery.of(context).size.height - 100,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(-5, 0),
            ),
          ],
        ),
        child: Consumer<SettingsProvider>(
          builder: (context, settings, child) {
            return Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إعدادات القراءة',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                  
                  const SizedBox(height: 30),
                  
                  // حجم الخط
                  const Text(
                    'حجم الخط',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(height: 10),
                  
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove_circle_outline),
                        onPressed: () => settings.setFontSize(settings.fontSize - 2),
                        color: const Color(0xFF2E7D32),
                      ),
                      Text(
                        '${settings.fontSize.toInt()}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add_circle_outline),
                        onPressed: () => settings.setFontSize(settings.fontSize + 2),
                        color: const Color(0xFF2E7D32),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // معاينة النص
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Text(
                      'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: settings.fontSize,
                        height: 2.0,
                        color: const Color(0xFF2E7D32),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _nextSurah() {
    if (_currentSurahIndex < _surahs.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousSurah() {
    if (_currentSurahIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showSurahsList() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // العنوان
            const Padding(
              padding: EdgeInsets.all(20),
              child: Text(
                'فهرس السور',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
            ),
            
            // قائمة السور
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                itemCount: _surahs.length,
                itemBuilder: (context, index) {
                  final surah = _surahs[index];
                  final isSelected = index == _currentSurahIndex;
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      color: isSelected 
                        ? const Color(0xFF2E7D32).withValues(alpha: 0.1)
                        : Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isSelected 
                            ? const Color(0xFF2E7D32)
                            : Colors.grey.shade200,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            '${surah.number}',
                            style: TextStyle(
                              color: isSelected ? Colors.white : Colors.black87,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                      title: Text(
                        surah.name,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                          color: isSelected ? const Color(0xFF2E7D32) : Colors.black87,
                        ),
                      ),
                      subtitle: Text(
                        '${surah.numberOfAyahs} آية • ${surah.revelationType}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        _goToSurah(index);
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _goToSurah(int index) {
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  void _showBookmarks() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة العلامات المرجعية قريباً'),
        backgroundColor: Color(0xFF2E7D32),
      ),
    );
  }

  void _showSearch() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة البحث قريباً'),
        backgroundColor: Color(0xFF2E7D32),
      ),
    );
  }
}