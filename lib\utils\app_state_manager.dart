import 'package:flutter/material.dart';
import 'error_handler.dart';
import 'app_constants.dart';

/// Centralized app state management
class AppStateManager extends ChangeNotifier {
  static final AppStateManager _instance = AppStateManager._internal();
  factory AppStateManager() => _instance;
  AppStateManager._internal();

  // App lifecycle state
  AppLifecycleState _lifecycleState = AppLifecycleState.resumed;
  AppLifecycleState get lifecycleState => _lifecycleState;

  // Connectivity state
  bool _isOnline = true;
  bool get isOnline => _isOnline;

  // Loading states
  final Map<String, bool> _loadingStates = {};
  
  // Error states
  final Map<String, String?> _errorStates = {};

  // Cache management
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};

  /// Update app lifecycle state
  void updateLifecycleState(AppLifecycleState state) {
    if (_lifecycleState != state) {
      _lifecycleState = state;
      _handleLifecycleChange(state);
      notifyListeners();
    }
  }

  /// Update connectivity state
  void updateConnectivity(bool isOnline) {
    if (_isOnline != isOnline) {
      _isOnline = isOnline;
      ErrorHandler.logError('Connectivity changed: ${isOnline ? 'Online' : 'Offline'}', null);
      notifyListeners();
    }
  }

  /// Set loading state for a specific operation
  void setLoading(String key, bool isLoading) {
    if (_loadingStates[key] != isLoading) {
      _loadingStates[key] = isLoading;
      notifyListeners();
    }
  }

  /// Get loading state for a specific operation
  bool isLoading(String key) => _loadingStates[key] ?? false;

  /// Set error state for a specific operation
  void setError(String key, String? error) {
    if (_errorStates[key] != error) {
      _errorStates[key] = error;
      if (error != null) {
        ErrorHandler.logError('State error for $key: $error', null);
      }
      notifyListeners();
    }
  }

  /// Get error state for a specific operation
  String? getError(String key) => _errorStates[key];

  /// Clear error state
  void clearError(String key) {
    if (_errorStates.containsKey(key)) {
      _errorStates.remove(key);
      notifyListeners();
    }
  }

  /// Clear all errors
  void clearAllErrors() {
    if (_errorStates.isNotEmpty) {
      _errorStates.clear();
      notifyListeners();
    }
  }

  /// Cache data with expiration
  void cacheData(String key, dynamic data, {Duration? expiration}) {
    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
    
    if (expiration != null) {
      Future.delayed(expiration, () => _removeCacheIfExpired(key));
    }
  }

  /// Get cached data
  T? getCachedData<T>(String key) {
    if (!_cache.containsKey(key)) return null;
    
    final timestamp = _cacheTimestamps[key];
    if (timestamp != null) {
      final age = DateTime.now().difference(timestamp);
      if (age > AppConstants.cacheExpiration) {
        _removeFromCache(key);
        return null;
      }
    }
    
    return _cache[key] as T?;
  }

  /// Check if data is cached and valid
  bool isCached(String key) {
    if (!_cache.containsKey(key)) return false;
    
    final timestamp = _cacheTimestamps[key];
    if (timestamp != null) {
      final age = DateTime.now().difference(timestamp);
      return age <= AppConstants.cacheExpiration;
    }
    
    return true;
  }

  /// Remove from cache
  void _removeFromCache(String key) {
    _cache.remove(key);
    _cacheTimestamps.remove(key);
  }

  /// Remove cache if expired
  void _removeCacheIfExpired(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp != null) {
      final age = DateTime.now().difference(timestamp);
      if (age > AppConstants.cacheExpiration) {
        _removeFromCache(key);
      }
    }
  }

  /// Clear all cache
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
    ErrorHandler.logError('Cache cleared', null);
  }

  /// Clear expired cache entries
  void clearExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTimestamps.entries) {
      final age = now.difference(entry.value);
      if (age > AppConstants.cacheExpiration) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _removeFromCache(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      ErrorHandler.logError('Cleared ${expiredKeys.length} expired cache entries', null);
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'totalEntries': _cache.length,
      'totalSize': _calculateCacheSize(),
      'oldestEntry': _getOldestCacheEntry(),
      'newestEntry': _getNewestCacheEntry(),
    };
  }

  /// Calculate approximate cache size
  int _calculateCacheSize() {
    // This is a rough estimation
    int size = 0;
    for (final value in _cache.values) {
      if (value is String) {
        size += value.length * 2; // UTF-16 encoding
      } else if (value is List) {
        size += value.length * 8; // Rough estimate
      } else if (value is Map) {
        size += value.length * 16; // Rough estimate
      } else {
        size += 8; // Default size for other types
      }
    }
    return size;
  }

  /// Get oldest cache entry timestamp
  DateTime? _getOldestCacheEntry() {
    if (_cacheTimestamps.isEmpty) return null;
    return _cacheTimestamps.values.reduce((a, b) => a.isBefore(b) ? a : b);
  }

  /// Get newest cache entry timestamp
  DateTime? _getNewestCacheEntry() {
    if (_cacheTimestamps.isEmpty) return null;
    return _cacheTimestamps.values.reduce((a, b) => a.isAfter(b) ? a : b);
  }

  /// Handle app lifecycle changes
  void _handleLifecycleChange(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.paused:
        // App is in background, save critical data
        _saveAppState();
        break;
      case AppLifecycleState.resumed:
        // App is back in foreground, refresh data if needed
        _refreshAppState();
        break;
      case AppLifecycleState.detached:
        // App is being terminated, cleanup
        _cleanupAppState();
        break;
      case AppLifecycleState.inactive:
        // App is inactive, prepare for potential background
        break;
      case AppLifecycleState.hidden:
        // App is hidden
        break;
    }
  }

  /// Save app state when going to background
  void _saveAppState() {
    ErrorHandler.logError('Saving app state', null);
    // Clear expired cache to free memory
    clearExpiredCache();
  }

  /// Refresh app state when coming to foreground
  void _refreshAppState() {
    ErrorHandler.logError('Refreshing app state', null);
    // Clear expired cache
    clearExpiredCache();
    // Notify listeners to refresh UI
    notifyListeners();
  }

  /// Cleanup app state when terminating
  void _cleanupAppState() {
    ErrorHandler.logError('Cleaning up app state', null);
    clearCache();
    clearAllErrors();
    _loadingStates.clear();
  }

  /// Execute operation with state management
  Future<T?> executeOperation<T>(
    String operationKey,
    Future<T> Function() operation, {
    bool useCache = false,
    Duration? cacheExpiration,
  }) async {
    // Check cache first if enabled
    if (useCache) {
      final cachedResult = getCachedData<T>(operationKey);
      if (cachedResult != null) {
        return cachedResult;
      }
    }

    // Set loading state
    setLoading(operationKey, true);
    clearError(operationKey);

    try {
      final result = await operation();
      
      // Cache result if successful and caching is enabled
      if (useCache && result != null) {
        cacheData(operationKey, result, expiration: cacheExpiration);
      }
      
      return result;
    } catch (error) {
      final errorMessage = ErrorHandler.handleApiError(error);
      setError(operationKey, errorMessage);
      return null;
    } finally {
      setLoading(operationKey, false);
    }
  }

  /// Get app state summary
  Map<String, dynamic> getAppStateSummary() {
    return {
      'lifecycleState': _lifecycleState.toString(),
      'isOnline': _isOnline,
      'loadingOperations': _loadingStates.keys.where((key) => _loadingStates[key] == true).toList(),
      'errorOperations': _errorStates.keys.where((key) => _errorStates[key] != null).toList(),
      'cacheStats': getCacheStats(),
    };
  }

  @override
  void dispose() {
    _cleanupAppState();
    super.dispose();
  }
}
