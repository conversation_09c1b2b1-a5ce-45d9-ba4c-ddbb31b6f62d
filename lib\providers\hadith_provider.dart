import 'package:flutter/material.dart';
import 'dart:math';
import '../models/hadith_models.dart';
import '../services/database_service.dart';
import '../services/hadith_api_service.dart';

class HadithProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService.instance;
  final HadithApiService _hadithApiService = HadithApiService();

  List<HadithCategory> _categories = [];
  final List<Hadith> _favoriteHadiths = [];
  HadithOfTheDay? _hadithOfTheDay;

  bool _isLoading = false;
  String? _errorMessage;

  // Current hadith being read
  HadithCategory? _currentCategory;
  Hadith? _currentHadith;
  int _currentHadithIndex = 0;

  // Search and filter
  String _searchQuery = '';
  String _selectedGrade = '';
  String _selectedSource = '';

  // Getters
  List<HadithCategory> get categories => _categories;
  List<Hadith> get favoriteHadiths => _favoriteHadiths;
  HadithOfTheDay? get hadithOfTheDay => _hadithOfTheDay;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  HadithCategory? get currentCategory => _currentCategory;
  Hadith? get currentHadith => _currentHadith;
  int get currentHadithIndex => _currentHadithIndex;
  String get searchQuery => _searchQuery;
  String get selectedGrade => _selectedGrade;
  String get selectedSource => _selectedSource;

  Future<void> initialize() async {
    setLoading(true);
    try {
      await _loadCategories();
      await _loadFavoriteHadiths();
      await _loadHadithOfTheDay();
    } catch (e) {
      _errorMessage = 'فشل في تحميل الأحاديث: $e';
    } finally {
      setLoading(false);
    }
  }

  Future<void> _loadCategories() async {
    try {
      // Load hadith from API
      _categories = await _hadithApiService.getAllHadithCategories();
    } catch (e) {
      // Fallback to default hadith
      _categories = HadithData.getDefaultCategories();
    }
    notifyListeners();
  }

  Future<void> _loadFavoriteHadiths() async {
    _favoriteHadiths.clear();

    for (final category in _categories) {
      for (final hadith in category.hadiths) {
        if (hadith.isFavorite) {
          _favoriteHadiths.add(hadith);
        }
      }
    }

    notifyListeners();
  }

  Future<void> _loadHadithOfTheDay() async {
    final today = DateTime.now();
    _hadithOfTheDay = await _databaseService.getHadithOfTheDay(today);

    if (_hadithOfTheDay == null) {
      await _generateHadithOfTheDay();
    }

    notifyListeners();
  }

  Future<void> _generateHadithOfTheDay() async {
    try {
      // Try to get random hadith from API
      final randomHadith = await _hadithApiService.getRandomHadith();

      if (randomHadith != null) {
        _hadithOfTheDay = HadithOfTheDay(
          hadith: randomHadith,
          date: DateTime.now(),
        );
      } else {
        // Fallback to local hadith
        final allHadiths = <Hadith>[];
        for (final category in _categories) {
          allHadiths.addAll(category.hadiths);
        }

        if (allHadiths.isNotEmpty) {
          final random = Random();
          final selectedHadith = allHadiths[random.nextInt(allHadiths.length)];

          _hadithOfTheDay = HadithOfTheDay(
            hadith: selectedHadith,
            date: DateTime.now(),
          );
        }
      }

      if (_hadithOfTheDay != null) {
        await _databaseService.saveHadithOfTheDay(_hadithOfTheDay!);
      }
    } catch (e) {
      // Handle error silently
    }
  }

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setCurrentCategory(HadithCategory category) {
    _currentCategory = category;
    _currentHadithIndex = 0;
    _currentHadith = category.hadiths.isNotEmpty ? category.hadiths.first : null;
    notifyListeners();
  }

  void setCurrentHadith(Hadith hadith) {
    _currentHadith = hadith;
    if (_currentCategory != null) {
      _currentHadithIndex = _currentCategory!.hadiths.indexOf(hadith);
    }
    notifyListeners();
  }

  void nextHadith() {
    if (_currentCategory == null || _currentCategory!.hadiths.isEmpty) return;

    _currentHadithIndex = (_currentHadithIndex + 1) % _currentCategory!.hadiths.length;
    _currentHadith = _currentCategory!.hadiths[_currentHadithIndex];
    notifyListeners();
  }

  void previousHadith() {
    if (_currentCategory == null || _currentCategory!.hadiths.isEmpty) return;

    _currentHadithIndex = (_currentHadithIndex - 1 + _currentCategory!.hadiths.length) % _currentCategory!.hadiths.length;
    _currentHadith = _currentCategory!.hadiths[_currentHadithIndex];
    notifyListeners();
  }

  // Favorite methods
  Future<void> toggleFavorite(Hadith hadith) async {
    // Find the hadith in categories and update it
    for (final category in _categories) {
      final index = category.hadiths.indexWhere((h) => h.id == hadith.id);
      if (index != -1) {
        final updatedHadith = Hadith(
          id: hadith.id,
          arabicText: hadith.arabicText,
          translation: hadith.translation,
          narrator: hadith.narrator,
          source: hadith.source,
          grade: hadith.grade,
          explanation: hadith.explanation,
          keywords: hadith.keywords,
          isFavorite: !hadith.isFavorite,
        );

        category.hadiths[index] = updatedHadith;

        if (_currentHadith?.id == hadith.id) {
          _currentHadith = updatedHadith;
        }

        break;
      }
    }

    await _loadFavoriteHadiths();
  }

  bool isFavorite(Hadith hadith) {
    return hadith.isFavorite;
  }

  // Search and filter methods
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setSelectedGrade(String grade) {
    _selectedGrade = grade;
    notifyListeners();
  }

  void setSelectedSource(String source) {
    _selectedSource = source;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _selectedGrade = '';
    _selectedSource = '';
    notifyListeners();
  }

  List<Hadith> getFilteredHadiths() {
    List<Hadith> allHadiths = [];

    for (final category in _categories) {
      allHadiths.addAll(category.hadiths);
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      allHadiths = allHadiths.where((hadith) =>
        hadith.arabicText.contains(_searchQuery) ||
        hadith.translation.contains(_searchQuery) ||
        hadith.narrator.contains(_searchQuery) ||
        hadith.keywords.any((keyword) => keyword.contains(_searchQuery))
      ).toList();
    }

    // Apply grade filter
    if (_selectedGrade.isNotEmpty) {
      allHadiths = allHadiths.where((hadith) => hadith.grade == _selectedGrade).toList();
    }

    // Apply source filter
    if (_selectedSource.isNotEmpty) {
      allHadiths = allHadiths.where((hadith) => hadith.source == _selectedSource).toList();
    }

    return allHadiths;
  }

  List<String> getAvailableGrades() {
    final grades = <String>{};
    for (final category in _categories) {
      for (final hadith in category.hadiths) {
        grades.add(hadith.grade);
      }
    }
    return grades.toList()..sort();
  }

  List<String> getAvailableSources() {
    final sources = <String>{};
    for (final category in _categories) {
      for (final hadith in category.hadiths) {
        sources.add(hadith.source);
      }
    }
    return sources.toList()..sort();
  }

  // Statistics methods
  int getTotalHadithsCount() {
    return _categories.fold(0, (total, category) => total + category.hadiths.length);
  }

  int getFavoriteHadithsCount() {
    return _favoriteHadiths.length;
  }

  Map<String, int> getHadithsByGrade() {
    final gradeCount = <String, int>{};

    for (final category in _categories) {
      for (final hadith in category.hadiths) {
        gradeCount[hadith.grade] = (gradeCount[hadith.grade] ?? 0) + 1;
      }
    }

    return gradeCount;
  }

  Map<String, int> getHadithsBySource() {
    final sourceCount = <String, int>{};

    for (final category in _categories) {
      for (final hadith in category.hadiths) {
        sourceCount[hadith.source] = (sourceCount[hadith.source] ?? 0) + 1;
      }
    }

    return sourceCount;
  }

  Map<String, int> getHadithsByCategory() {
    final categoryCount = <String, int>{};

    for (final category in _categories) {
      categoryCount[category.name] = category.hadiths.length;
    }

    return categoryCount;
  }

  // Random hadith
  Hadith? getRandomHadith() {
    final allHadiths = <Hadith>[];
    for (final category in _categories) {
      allHadiths.addAll(category.hadiths);
    }

    if (allHadiths.isEmpty) return null;

    final random = Random();
    return allHadiths[random.nextInt(allHadiths.length)];
  }

  // Share hadith
  String formatHadithForSharing(Hadith hadith) {
    return '''
${hadith.arabicText}

الترجمة: ${hadith.translation}

الراوي: ${hadith.narrator}
المصدر: ${hadith.source}
الدرجة: ${hadith.grade}

تطبيق قرآني 📱
''';
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
