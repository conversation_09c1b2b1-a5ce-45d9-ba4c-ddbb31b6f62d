import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Enhanced cache manager for API data
class CacheManager {
  static const String _quranCacheKey = 'quran_cache';
  static const String _hadithCacheKey = 'hadith_cache';
  static const String _azkarCacheKey = 'azkar_cache';
  static const String _prayerTimesCacheKey = 'prayer_times_cache';
  
  static const Duration _defaultCacheDuration = Duration(hours: 24);
  static const Duration _prayerTimesCacheDuration = Duration(hours: 1);

  /// Save data to cache with timestamp
  static Future<void> saveToCache(String key, dynamic data, {Duration? duration}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'duration': (duration ?? _defaultCacheDuration).inMilliseconds,
      };
      
      await prefs.setString(key, json.encode(cacheData));
    } catch (e) {
      print('Error saving to cache: $e');
    }
  }

  /// Get data from cache if not expired
  static Future<T?> getFromCache<T>(String key, T Function(dynamic) fromJson) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheString = prefs.getString(key);
      
      if (cacheString == null) return null;
      
      final cacheData = json.decode(cacheString);
      final timestamp = cacheData['timestamp'] as int;
      final duration = cacheData['duration'] as int;
      
      // Check if cache is expired
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final expiryTime = cacheTime.add(Duration(milliseconds: duration));
      
      if (DateTime.now().isAfter(expiryTime)) {
        // Cache expired, remove it
        await prefs.remove(key);
        return null;
      }
      
      return fromJson(cacheData['data']);
    } catch (e) {
      print('Error reading from cache: $e');
      return null;
    }
  }

  /// Check if cache exists and is valid
  static Future<bool> isCacheValid(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheString = prefs.getString(key);
      
      if (cacheString == null) return false;
      
      final cacheData = json.decode(cacheString);
      final timestamp = cacheData['timestamp'] as int;
      final duration = cacheData['duration'] as int;
      
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final expiryTime = cacheTime.add(Duration(milliseconds: duration));
      
      return DateTime.now().isBefore(expiryTime);
    } catch (e) {
      return false;
    }
  }

  /// Clear specific cache
  static Future<void> clearCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(key);
    } catch (e) {
      print('Error clearing cache: $e');
    }
  }

  /// Clear all caches
  static Future<void> clearAllCaches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.remove(_quranCacheKey),
        prefs.remove(_hadithCacheKey),
        prefs.remove(_azkarCacheKey),
        prefs.remove(_prayerTimesCacheKey),
      ]);
    } catch (e) {
      print('Error clearing all caches: $e');
    }
  }

  /// Get cache size in bytes
  static Future<int> getCacheSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      int totalSize = 0;
      
      for (String key in [_quranCacheKey, _hadithCacheKey, _azkarCacheKey, _prayerTimesCacheKey]) {
        final value = prefs.getString(key);
        if (value != null) {
          totalSize += value.length;
        }
      }
      
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// Get cache info
  static Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      Map<String, dynamic> info = {};
      
      for (String key in [_quranCacheKey, _hadithCacheKey, _azkarCacheKey, _prayerTimesCacheKey]) {
        final cacheString = prefs.getString(key);
        if (cacheString != null) {
          try {
            final cacheData = json.decode(cacheString);
            final timestamp = cacheData['timestamp'] as int;
            final duration = cacheData['duration'] as int;
            
            final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
            final expiryTime = cacheTime.add(Duration(milliseconds: duration));
            
            info[key] = {
              'size': cacheString.length,
              'created': cacheTime.toIso8601String(),
              'expires': expiryTime.toIso8601String(),
              'valid': DateTime.now().isBefore(expiryTime),
            };
          } catch (e) {
            info[key] = {'error': 'Invalid cache data'};
          }
        }
      }
      
      return info;
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  // Specific cache methods for each data type
  
  /// Save Quran data to cache
  static Future<void> saveQuranCache(dynamic data) async {
    await saveToCache(_quranCacheKey, data);
  }

  /// Get Quran data from cache
  static Future<T?> getQuranCache<T>(T Function(dynamic) fromJson) async {
    return await getFromCache(_quranCacheKey, fromJson);
  }

  /// Save Hadith data to cache
  static Future<void> saveHadithCache(dynamic data) async {
    await saveToCache(_hadithCacheKey, data);
  }

  /// Get Hadith data from cache
  static Future<T?> getHadithCache<T>(T Function(dynamic) fromJson) async {
    return await getFromCache(_hadithCacheKey, fromJson);
  }

  /// Save Azkar data to cache
  static Future<void> saveAzkarCache(dynamic data) async {
    await saveToCache(_azkarCacheKey, data);
  }

  /// Get Azkar data from cache
  static Future<T?> getAzkarCache<T>(T Function(dynamic) fromJson) async {
    return await getFromCache(_azkarCacheKey, fromJson);
  }

  /// Save Prayer times to cache
  static Future<void> savePrayerTimesCache(dynamic data) async {
    await saveToCache(_prayerTimesCacheKey, data, duration: _prayerTimesCacheDuration);
  }

  /// Get Prayer times from cache
  static Future<T?> getPrayerTimesCache<T>(T Function(dynamic) fromJson) async {
    return await getFromCache(_prayerTimesCacheKey, fromJson);
  }
}

/// Cache statistics
class CacheStats {
  final int totalSize;
  final int validCaches;
  final int expiredCaches;
  final Map<String, dynamic> details;

  CacheStats({
    required this.totalSize,
    required this.validCaches,
    required this.expiredCaches,
    required this.details,
  });

  /// Get human readable size
  String get readableSize {
    if (totalSize < 1024) return '$totalSize B';
    if (totalSize < 1024 * 1024) return '${(totalSize / 1024).toStringAsFixed(1)} KB';
    return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
