class NullSafetyHelper {
  // استخدام قيم افتراضية بدلاً من عوامل التشغيل null-aware
  static T valueOrDefault<T>(T? value, T defaultValue) {
    return value ?? defaultValue;
  }
  
  // التحقق من القيم قبل استخدامها
  static bool isNullOrEmpty(String? text) {
    return text == null || text.isEmpty;
  }
  
  // تحويل آمن للقيم
  static int? tryParseInt(String? text) {
    if (text == null) return null;
    return int.tryParse(text);
  }
  
  static double? tryParseDouble(String? text) {
    if (text == null) return null;
    return double.tryParse(text);
  }
}