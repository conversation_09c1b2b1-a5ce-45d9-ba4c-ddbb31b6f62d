import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/settings_provider.dart';
import '../../utils/rtl_helper.dart';

class ReadingSettingsScreen extends StatefulWidget {
  const ReadingSettingsScreen({super.key});

  @override
  State<ReadingSettingsScreen> createState() => _ReadingSettingsScreenState();
}

class _ReadingSettingsScreenState extends State<ReadingSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Scaffold(
          appBar: AppBar(
            title: RTLHelper.buildDirectionalText(
              'إعدادات القراءة',
              style: const TextStyle(fontWeight: FontWeight.bold),
              forceRTL: true,
            ),
            leading: IconButton(
              icon: RTLHelper.buildRTLIcon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: RTLHelper.buildResponsiveRTLLayout(
            context: context,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // وضع القراءة
                  _buildSectionTitle('وضع القراءة', Icons.brightness_6),
                  const SizedBox(height: 16),
                  _buildReadingModeSelector(settings),
                  const SizedBox(height: 32),

                  // الخط
                  _buildSectionTitle('نوع الخط', Icons.font_download),
                  const SizedBox(height: 16),
                  _buildFontSelector(settings),
                  const SizedBox(height: 32),

                  // حجم الخط
                  _buildSectionTitle('حجم الخط', Icons.format_size),
                  const SizedBox(height: 16),
                  _buildFontSizeSlider(settings),
                  const SizedBox(height: 32),

                  // ارتفاع السطر
                  _buildSectionTitle('ارتفاع السطر', Icons.format_line_spacing),
                  const SizedBox(height: 16),
                  _buildLineHeightSlider(settings),
                  const SizedBox(height: 32),

                  // إعدادات إضافية
                  _buildSectionTitle('إعدادات إضافية', Icons.tune),
                  const SizedBox(height: 16),
                  _buildAdditionalSettings(settings),
                  const SizedBox(height: 32),

                  // معاينة النص
                  _buildSectionTitle('معاينة النص', Icons.preview),
                  const SizedBox(height: 16),
                  _buildTextPreview(settings),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return RTLHelper.buildRTLRow(
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 24,
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildReadingModeSelector(SettingsProvider settings) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: settings.getAvailableReadingModes().map((mode) {
            return RadioListTile<String>(
              title: RTLHelper.buildDirectionalText(
                settings.getReadingModeDisplayName(mode),
                forceRTL: true,
              ),
              subtitle: Text(_getModeDescription(mode)),
              value: mode,
              groupValue: settings.readingMode,
              onChanged: (value) {
                if (value != null) {
                  settings.setReadingMode(value);
                }
              },
              activeColor: Theme.of(context).primaryColor,
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildFontSelector(SettingsProvider settings) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: settings.getAvailableFonts().map((font) {
            return RadioListTile<String>(
              title: Text(
                font,
                style: TextStyle(
                  fontFamily: settings.getFontFamilyName(),
                  fontSize: 18,
                ),
              ),
              value: font,
              groupValue: settings.fontFamily,
              onChanged: (value) {
                if (value != null) {
                  settings.setFontFamily(value);
                }
              },
              activeColor: Theme.of(context).primaryColor,
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildFontSizeSlider(SettingsProvider settings) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            RTLHelper.buildRTLRow(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الحجم: ${settings.fontSize.toInt()}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                Text(
                  settings.getFontSizeCategory(),
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Slider(
              value: settings.fontSize,
              min: 12,
              max: 32,
              divisions: 10,
              onChanged: (value) {
                settings.setFontSize(value);
              },
              activeColor: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLineHeightSlider(SettingsProvider settings) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            RTLHelper.buildRTLRow(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الارتفاع: ${settings.lineHeight.toStringAsFixed(1)}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                Text(
                  _getLineHeightDescription(settings.lineHeight),
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Slider(
              value: settings.lineHeight,
              min: 1.2,
              max: 2.5,
              divisions: 13,
              onChanged: (value) {
                settings.setLineHeight(value);
              },
              activeColor: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalSettings(SettingsProvider settings) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SwitchListTile(
              title: RTLHelper.buildDirectionalText(
                'التمرير التلقائي',
                forceRTL: true,
              ),
              subtitle: const Text('تمرير الصفحة تلقائياً أثناء القراءة'),
              value: settings.autoScroll,
              onChanged: (value) {
                settings.setAutoScroll(value);
              },
              activeColor: Theme.of(context).primaryColor,
            ),
            const Divider(),
            SwitchListTile(
              title: RTLHelper.buildDirectionalText(
                'عرض الترجمة',
                forceRTL: true,
              ),
              subtitle: const Text('إظهار ترجمة معاني الآيات'),
              value: settings.showTranslation,
              onChanged: (value) {
                settings.setShowTranslation(value);
              },
              activeColor: Theme.of(context).primaryColor,
            ),
            const Divider(),
            SwitchListTile(
              title: RTLHelper.buildDirectionalText(
                'عرض التفسير',
                forceRTL: true,
              ),
              subtitle: const Text('إظهار تفسير مبسط للآيات'),
              value: settings.showTafsir,
              onChanged: (value) {
                settings.setShowTafsir(value);
              },
              activeColor: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextPreview(SettingsProvider settings) {
    return Card(
      elevation: 2,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: settings.getReadingBackgroundColor(),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Text(
              'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
              style: TextStyle(
                fontFamily: settings.getFontFamilyName(),
                fontSize: settings.fontSize + 2,
                color: settings.getReadingTextColor(),
                height: settings.lineHeight,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 16),
            Text(
              'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ ﴿٢﴾ الرَّحْمَٰنِ الرَّحِيمِ ﴿٣﴾ مَالِكِ يَوْمِ الدِّينِ ﴿٤﴾',
              style: TextStyle(
                fontFamily: settings.getFontFamilyName(),
                fontSize: settings.fontSize,
                color: settings.getReadingTextColor(),
                height: settings.lineHeight,
              ),
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }

  String _getModeDescription(String mode) {
    switch (mode) {
      case 'night':
        return 'خلفية داكنة مريحة للعين في الإضاءة المنخفضة';
      case 'sepia':
        return 'خلفية بلون السيبيا تقلل إجهاد العين';
      default:
        return 'خلفية بيضاء تقليدية للقراءة النهارية';
    }
  }

  String _getLineHeightDescription(double height) {
    if (height <= 1.4) return 'مضغوط';
    if (height <= 1.8) return 'متوسط';
    if (height <= 2.2) return 'مريح';
    return 'واسع';
  }
}
