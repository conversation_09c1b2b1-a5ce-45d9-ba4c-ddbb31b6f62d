#!/usr/bin/env python3
"""
Script to generate app icons from SVG
Requires: pip install cairosvg pillow
"""

import os
import sys
from pathlib import Path

try:
    import cairosvg
    from PIL import Image, ImageDraw, ImageFilter
except ImportError:
    print("Required packages not found. Please install:")
    print("pip install cairosvg pillow")
    sys.exit(1)

def create_icon_with_background(size, output_path):
    """Create a beautiful Islamic app icon"""
    
    # Create a new image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Background gradient (Islamic green)
    center = size // 2
    radius = size // 2 - 20
    
    # Create radial gradient background
    for i in range(radius):
        alpha = 255 - int((i / radius) * 100)
        green_intensity = 76 + int((i / radius) * 50)  # From #4CAF50 to #2E7D32
        color = (46, 125, 50, alpha)
        draw.ellipse([center-radius+i, center-radius+i, center+radius-i, center+radius-i], 
                    fill=color, outline=None)
    
    # Outer border
    draw.ellipse([10, 10, size-10, size-10], outline=(27, 94, 32, 255), width=8)
    
    # Inner decorative circles
    draw.ellipse([center-200, center-200, center+200, center+200], 
                outline=(255, 215, 0, 50), width=3)
    draw.ellipse([center-180, center-180, center+180, center+180], 
                outline=(255, 215, 0, 30), width=2)
    
    # Book shape (simplified)
    book_width = 200
    book_height = 130
    book_x = center - book_width // 2
    book_y = center - book_height // 2 + 30
    
    # Book shadow
    shadow_offset = 10
    draw.ellipse([book_x + shadow_offset, book_y + book_height + shadow_offset, 
                 book_x + book_width + shadow_offset, book_y + book_height + 30], 
                fill=(0, 0, 0, 50))
    
    # Left page
    left_page = [
        (book_x, book_y + 20),
        (book_x, book_y + book_height - 20),
        (book_x + book_width//2 - 5, book_y + book_height),
        (book_x + book_width//2 - 5, book_y)
    ]
    draw.polygon(left_page, fill=(255, 248, 225, 255), outline=(224, 224, 224, 255))
    
    # Right page
    right_page = [
        (book_x + book_width//2 + 5, book_y),
        (book_x + book_width//2 + 5, book_y + book_height),
        (book_x + book_width, book_y + book_height - 20),
        (book_x + book_width, book_y + 20)
    ]
    draw.polygon(right_page, fill=(255, 248, 225, 255), outline=(224, 224, 224, 255))
    
    # Book spine
    spine_rect = [book_x + book_width//2 - 5, book_y, 
                  book_x + book_width//2 + 5, book_y + book_height]
    draw.rectangle(spine_rect, fill=(141, 110, 99, 255))
    
    # Add some text lines (simplified)
    line_color = (224, 224, 224, 255)
    for i in range(5):
        y_pos = book_y + 40 + i * 15
        # Left page lines
        draw.rectangle([book_x + 20, y_pos, book_x + book_width//2 - 15, y_pos + 2], 
                      fill=line_color)
        # Right page lines
        draw.rectangle([book_x + book_width//2 + 15, y_pos, book_x + book_width - 20, y_pos + 2], 
                      fill=line_color)
    
    # Add decorative stars
    star_positions = [
        (center - 106, center - 136),
        (center + 106, center - 136),
        (center - 156, center + 44),
        (center + 156, center + 44)
    ]
    
    for star_x, star_y in star_positions:
        # Simple star shape
        star_size = 15
        star_points = []
        for i in range(10):
            angle = i * 36  # 360/10
            if i % 2 == 0:
                radius = star_size
            else:
                radius = star_size // 2
            import math
            x = star_x + radius * math.cos(math.radians(angle - 90))
            y = star_y + radius * math.sin(math.radians(angle - 90))
            star_points.append((x, y))
        
        draw.polygon(star_points, fill=(255, 215, 0, 200))
    
    # Add outer glow
    glow_img = img.copy()
    glow_img = glow_img.filter(ImageFilter.GaussianBlur(radius=3))
    
    # Combine original with glow
    final_img = Image.alpha_composite(glow_img, img)
    
    # Save the image
    final_img.save(output_path, 'PNG', quality=100)
    print(f"Created icon: {output_path}")

def main():
    # Create assets/icon directory if it doesn't exist
    icon_dir = Path("assets/icon")
    icon_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate main icon
    create_icon_with_background(1024, "assets/icon/app_icon.png")
    
    # Generate adaptive icon foreground (for Android)
    create_icon_with_background(432, "assets/icon/app_icon_foreground.png")
    
    print("App icons generated successfully!")
    print("Files created:")
    print("- assets/icon/app_icon.png (1024x1024)")
    print("- assets/icon/app_icon_foreground.png (432x432)")

if __name__ == "__main__":
    main()
