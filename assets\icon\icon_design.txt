🎨 تصميم أيقونة تطبيق قرآني
═══════════════════════════════════

📐 المواصفات:
- الحجم: 1024x1024 بكسل
- التنسيق: PNG
- الخلفية: دائرية

🎨 التصميم المرئي:
┌─────────────────────────────────────┐
│    ╭─────────────────────────────╮   │
│  ╭─┴─────────────────────────────┴─╮ │
│ ╱                                 ╲│
│╱           ⭐        ⭐           ╲│
││                                  ││
││              قرآني               ││
││                                  ││
││         ╭─────┬─────╮            ││
││         │ بسم │الحمد│            ││
││         │ الله│ لله │            ││
││         │الرحمن│ رب │            ││
││         │الرحيم│العالمين│         ││
││         │ ─── │ ─── │            ││
││         │ ─── │ ─── │            ││
││         ╰─────┴─────╯            ││
││                                  ││
│╲           ⭐        ⭐           ╱│
│ ╲─┬─────────────────────────────┬─╱ │
│  ╰─┴─────────────────────────────┴─╯ │
└─────────────────────────────────────┘

🌈 الألوان:
┌─────────────────┬─────────────┐
│ العنصر          │ اللون       │
├─────────────────┼─────────────┤
│ الخلفية الخارجية │ #4CAF50     │
│ الخلفية الداخلية │ #2E7D32     │
│ الحدود          │ #1B5E20     │
│ النص "قرآني"    │ #FFD700     │
│ صفحات المصحف    │ #FFF8E1     │
│ النص القرآني    │ #2E7D32     │
│ النجوم          │ #FFD700     │
│ التوهج الخارجي   │ #FFD700     │
└─────────────────┴─────────────┘

📝 النصوص:
- العنوان: "قرآني" (خط كبير، ذهبي، وسط)
- الصفحة اليسرى: "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ"
- الصفحة اليمنى: "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ"

🔧 التفاصيل التقنية:
- دائرة خارجية: نصف قطر 480px
- دائرة الحدود: عرض 16px
- المصحف: 200x140px
- النجوم: 4 نجوم في الزوايا
- الخط: Serif للنصوص العربية

✨ التأثيرات:
- تدرج شعاعي للخلفية
- ظل للمصحف
- توهج ذهبي خارجي
- دوائر زخرفية شفافة

🎯 الهدف:
إنشاء أيقونة إسلامية أنيقة تعكس:
- الطابع الديني للتطبيق
- الجودة والاحترافية
- الوضوح في جميع الأحجام
- التوافق مع معايير المتاجر

📱 الاستخدام:
1. افتح create_simple_icon.html
2. حمل الأيقونة بحجم 1024x1024
3. احفظها كـ app_icon.png
4. شغل flutter pub run flutter_launcher_icons

🏆 النتيجة المتوقعة:
أيقونة احترافية جاهزة للنشر على:
- Google Play Store
- Apple App Store
- مواقع التحميل الأخرى
