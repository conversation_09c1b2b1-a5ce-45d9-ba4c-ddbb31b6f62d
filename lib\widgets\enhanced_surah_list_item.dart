import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/audio_provider.dart';
import '../models/quran_models.dart';
import '../screens/quran/surah_reading_screen.dart';
import '../utils/rtl_helper.dart';
import 'animated_card.dart';

class EnhancedSurahListItem extends StatefulWidget {
  final Surah surah;
  final int index;
  final bool isLastRead;
  final VoidCallback? onDownload;

  const EnhancedSurahListItem({
    super.key,
    required this.surah,
    required this.index,
    this.isLastRead = false,
    this.onDownload,
  });

  @override
  State<EnhancedSurahListItem> createState() => _EnhancedSurahListItemState();
}

class _EnhancedSurahListItemState extends State<EnhancedSurahListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isBookmarked = false;
  final bool _isDownloaded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _checkBookmarkStatus();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkBookmarkStatus() async {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    final isBookmarked = await quranProvider.isBookmarked(widget.surah.number, 1);
    if (mounted) {
      setState(() {
        _isBookmarked = isBookmarked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<QuranProvider, SettingsProvider, AudioProvider>(
      builder: (context, quranProvider, settings, audioProvider, child) {
        final isCurrentlyPlaying = audioProvider.currentSurah?.number == widget.surah.number;
        final theme = Theme.of(context);

        return AnimatedListCard(
          index: widget.index,
          onTap: () => _onSurahTap(context, quranProvider),
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: settings.enableAnimations ? _scaleAnimation.value : 1.0,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      gradient: _buildGradient(theme, isCurrentlyPlaying, widget.isLastRead),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: theme.colorScheme.primary.withValues(alpha: 0.2),
                          blurRadius: isCurrentlyPlaying ? 12 : 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                      border: widget.isLastRead
                          ? Border.all(
                              color: theme.colorScheme.primary,
                              width: 2,
                            )
                          : null,
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(20),
                        onTap: () => _onSurahTap(context, quranProvider),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: RTLHelper.buildRTLRow(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // معلومات السورة
                              Expanded(
                                child: RTLHelper.buildRTLRow(
                                  children: [
                                    // رقم السورة
                                    _buildSurahNumber(theme, isCurrentlyPlaying),
                                    const SizedBox(width: 16),

                                    // تفاصيل السورة
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          // اسم السورة
                                          RTLHelper.buildDirectionalText(
                                            widget.surah.name,
                                            style: TextStyle(
                                              fontSize: settings.fontSize + 2,
                                              fontWeight: FontWeight.bold,
                                              color: isCurrentlyPlaying
                                                  ? Colors.white
                                                  : theme.colorScheme.onSurface,
                                            ),
                                            forceRTL: true,
                                          ),
                                          const SizedBox(height: 4),

                                          // الاسم الإنجليزي
                                          Text(
                                            widget.surah.englishNameTranslation,
                                            style: TextStyle(
                                              fontSize: settings.fontSize - 2,
                                              color: isCurrentlyPlaying
                                                  ? Colors.white70
                                                  : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                                            ),
                                          ),
                                          const SizedBox(height: 4),

                                          // معلومات إضافية
                                          RTLHelper.buildRTLRow(
                                            children: [
                                              _buildInfoChip(
                                                '${widget.surah.numberOfAyahs} آية',
                                                Icons.format_list_numbered,
                                                isCurrentlyPlaying,
                                                theme,
                                              ),
                                              const SizedBox(width: 8),
                                              _buildInfoChip(
                                                widget.surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
                                                widget.surah.revelationType == 'Meccan'
                                                    ? Icons.location_on
                                                    : Icons.location_city,
                                                isCurrentlyPlaying,
                                                theme,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // أزرار التحكم
                              Column(
                                children: [
                                  // أزرار الإجراءات العلوية
                                  RTLHelper.buildRTLRow(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      _buildActionButton(
                                        icon: _isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                                        onPressed: () => _toggleBookmark(quranProvider),
                                        isActive: _isBookmarked,
                                        theme: theme,
                                        isCurrentlyPlaying: isCurrentlyPlaying,
                                      ),
                                      const SizedBox(width: 8),
                                      _buildActionButton(
                                        icon: Icons.download,
                                        onPressed: widget.onDownload,
                                        isActive: _isDownloaded,
                                        theme: theme,
                                        isCurrentlyPlaying: isCurrentlyPlaying,
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),

                                  // زر التشغيل الرئيسي
                                  _buildPlayButton(audioProvider, theme, isCurrentlyPlaying),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  LinearGradient _buildGradient(ThemeData theme, bool isCurrentlyPlaying, bool isLastRead) {
    if (isCurrentlyPlaying) {
      return LinearGradient(
        colors: [
          theme.colorScheme.primary,
          theme.colorScheme.primary.withValues(alpha: 0.8),
        ],
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
      );
    } else if (isLastRead) {
      return LinearGradient(
        colors: [
          theme.colorScheme.primaryContainer,
          theme.colorScheme.primaryContainer.withValues(alpha: 0.7),
        ],
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
      );
    } else {
      return LinearGradient(
        colors: [
          theme.colorScheme.surface,
          theme.colorScheme.surface,
        ],
      );
    }
  }

  Widget _buildSurahNumber(ThemeData theme, bool isCurrentlyPlaying) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: isCurrentlyPlaying
            ? Colors.white.withValues(alpha: 0.2)
            : theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: isCurrentlyPlaying
              ? Colors.white.withValues(alpha: 0.5)
              : theme.colorScheme.primary.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          '${widget.surah.number}',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isCurrentlyPlaying
                ? Colors.white
                : theme.colorScheme.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(String text, IconData icon, bool isCurrentlyPlaying, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isCurrentlyPlaying
            ? Colors.white.withValues(alpha: 0.2)
            : theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: RTLHelper.buildRTLRow(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: isCurrentlyPlaying
                ? Colors.white70
                : theme.colorScheme.primary,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: isCurrentlyPlaying
                  ? Colors.white70
                  : theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isActive,
    required ThemeData theme,
    required bool isCurrentlyPlaying,
  }) {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        color: isActive
            ? (isCurrentlyPlaying ? Colors.white : theme.colorScheme.primary)
            : (isCurrentlyPlaying
                ? Colors.white.withValues(alpha: 0.2)
                : theme.colorScheme.primary.withValues(alpha: 0.1)),
        borderRadius: BorderRadius.circular(18),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          size: 18,
          color: isActive
              ? (isCurrentlyPlaying ? theme.colorScheme.primary : Colors.white)
              : (isCurrentlyPlaying
                  ? Colors.white70
                  : theme.colorScheme.primary),
        ),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildPlayButton(AudioProvider audioProvider, ThemeData theme, bool isCurrentlyPlaying) {
    final isPlaying = isCurrentlyPlaying && audioProvider.isPlaying;

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isCurrentlyPlaying
              ? [Colors.white, Colors.white.withValues(alpha: 0.9)]
              : [theme.colorScheme.primary, theme.colorScheme.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: (isCurrentlyPlaying ? Colors.white : theme.colorScheme.primary)
                .withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(
          isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
          color: isCurrentlyPlaying ? theme.colorScheme.primary : Colors.white,
          size: 24,
        ),
        onPressed: () => _onPlayButtonPressed(audioProvider),
        padding: EdgeInsets.zero,
      ),
    );
  }

  void _onSurahTap(BuildContext context, QuranProvider quranProvider) async {
    quranProvider.setCurrentSurah(widget.surah);

    // Load full surah data if needed
    Surah? fullSurah = widget.surah;
    if (widget.surah.ayahs.isEmpty) {
      fullSurah = await quranProvider.loadSurah(widget.surah.number);
    }

    if (context.mounted && fullSurah != null) {
      Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              SurahReadingScreen(surah: fullSurah!),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        ),
      );
    } else if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('فشل في تحميل السورة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onPlayButtonPressed(AudioProvider audioProvider) async {
    // تهيئة قائمة السور في AudioProvider إذا لم تكن مهيأة
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    if (quranProvider.surahs.isNotEmpty) {
      audioProvider.initializeSurahs(quranProvider.surahs);
    }

    if (audioProvider.currentSurah?.number == widget.surah.number) {
      // نفس السورة - تشغيل/إيقاف
      if (audioProvider.isPlaying) {
        audioProvider.pause();
      } else {
        audioProvider.resume();
      }
    } else {
      // سورة جديدة - بدء التشغيل
      final settings = Provider.of<SettingsProvider>(context, listen: false);
      final selectedReciter = settings.getSelectedReciter();

      if (selectedReciter != null) {
        await audioProvider.playSurah(widget.surah, selectedReciter);
      } else {
        // استخدام القارئ الافتراضي
        final reciters = quranProvider.reciters;
        if (reciters.isNotEmpty) {
          await audioProvider.playSurah(widget.surah, reciters.first);
        }
      }
    }
  }

  void _toggleBookmark(QuranProvider quranProvider) async {
    if (_isBookmarked) {
      await quranProvider.removeBookmark(widget.surah.number, 1);
    } else {
      await quranProvider.addBookmark(widget.surah.number, 1);
    }

    setState(() {
      _isBookmarked = !_isBookmarked;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isBookmarked
                ? 'تم إضافة ${widget.surah.name} للمفضلة'
                : 'تم إزالة ${widget.surah.name} من المفضلة',
          ),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}
