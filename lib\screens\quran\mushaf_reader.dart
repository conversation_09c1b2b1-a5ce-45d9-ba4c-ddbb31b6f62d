import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/settings_provider.dart';

class MushafReader extends StatefulWidget {
  const MushafReader({super.key});

  @override
  State<MushafReader> createState() => _MushafReaderState();
}

class _MushafReaderState extends State<MushafReader> {
  final PageController _pageController = PageController();
  int _currentPage = 1;
  final int _totalPages = 604; // عدد صفحات المصحف الشريف

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5DC), // لون كريمي يشبه الورق القديم
      appBar: AppBar(
        title: const Text('المصحف الشريف'),
        backgroundColor: const Color(0xFF8B4513), // لون بني يشبه الجلد
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_border),
            onPressed: _showBookmarks,
            tooltip: 'العلامات المرجعية',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showPageSelector,
            tooltip: 'الانتقال لصفحة',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettings,
            tooltip: 'الإعدادات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط معلومات الصفحة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFF8B4513).withValues(alpha: 0.1),
              border: Border(
                bottom: BorderSide(
                  color: const Color(0xFF8B4513).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // معلومات الجزء والحزب
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الجزء ${_getJuzNumber(_currentPage)}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF8B4513),
                      ),
                    ),
                    Text(
                      'الحزب ${_getHizbNumber(_currentPage)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.brown.shade600,
                      ),
                    ),
                  ],
                ),
                
                // رقم الصفحة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF8B4513),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'صفحة $_currentPage',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                
                // معلومات السورة
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      _getSurahName(_currentPage),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF8B4513),
                      ),
                    ),
                    Text(
                      'آية ${_getAyahRange(_currentPage)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.brown.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // صفحات المصحف
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (page) {
                setState(() {
                  _currentPage = page + 1;
                });
              },
              itemCount: _totalPages,
              itemBuilder: (context, index) {
                return _buildMushafPage(index + 1);
              },
            ),
          ),
          
          // شريط التنقل السفلي
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFF8B4513).withValues(alpha: 0.1),
              border: Border(
                top: BorderSide(
                  color: const Color(0xFF8B4513).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // الصفحة السابقة
                IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: _currentPage > 1 ? _previousPage : null,
                  color: _currentPage > 1 
                    ? const Color(0xFF8B4513) 
                    : Colors.grey,
                  iconSize: 28,
                ),
                
                // مؤشر الصفحات
                Expanded(
                  child: Container(
                    height: 4,
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: _currentPage / _totalPages,
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFF8B4513),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                  ),
                ),
                
                // الصفحة التالية
                IconButton(
                  icon: const Icon(Icons.arrow_forward_ios),
                  onPressed: _currentPage < _totalPages ? _nextPage : null,
                  color: _currentPage < _totalPages 
                    ? const Color(0xFF8B4513) 
                    : Colors.grey,
                  iconSize: 28,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMushafPage(int pageNumber) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
            border: Border.all(
              color: const Color(0xFF8B4513).withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Column(
              children: [
                // رأس الصفحة
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF8B4513).withValues(alpha: 0.1),
                        const Color(0xFF8B4513).withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  child: Text(
                    _getSurahName(pageNumber),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF8B4513),
                    ),
                  ),
                ),
                
                // محتوى الصفحة
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: _buildPageContent(pageNumber, settings),
                  ),
                ),
                
                // رقم الصفحة في الأسفل
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF8B4513).withValues(alpha: 0.05),
                        const Color(0xFF8B4513).withValues(alpha: 0.1),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  child: Text(
                    '$pageNumber',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF8B4513),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPageContent(int pageNumber, SettingsProvider settings) {
    // محتوى الصفحة حسب رقم الصفحة
    if (pageNumber == 1) {
      return _buildFatihaPage(settings);
    } else if (pageNumber <= 10) {
      return _buildBaqarahPages(pageNumber, settings);
    } else {
      return _buildGeneralPage(pageNumber, settings);
    }
  }

  Widget _buildFatihaPage(SettingsProvider settings) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // البسملة
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: const Color(0xFF8B4513).withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF8B4513).withValues(alpha: 0.2),
              ),
            ),
            child: Text(
              'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: settings.fontSize + 4,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF8B4513),
                height: 2.0,
              ),
            ),
          ),
          
          // آيات الفاتحة
          _buildAyahContainer('الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ', 1, settings),
          _buildAyahContainer('الرَّحْمَٰنِ الرَّحِيمِ', 2, settings),
          _buildAyahContainer('مَالِكِ يَوْمِ الدِّينِ', 3, settings),
          _buildAyahContainer('إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ', 4, settings),
          _buildAyahContainer('اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ', 5, settings),
          _buildAyahContainer('صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ', 6, settings),
        ],
      ),
    );
  }

  Widget _buildBaqarahPages(int pageNumber, SettingsProvider settings) {
    // صفحات سورة البقرة
    final ayahs = _getBaqarahAyahs(pageNumber);
    
    return SingleChildScrollView(
      child: Column(
        children: [
          if (pageNumber == 2) // البسملة في بداية البقرة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: const Color(0xFF8B4513).withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: settings.fontSize + 2,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF8B4513),
                ),
              ),
            ),
          
          ...ayahs.map((ayah) => _buildAyahContainer(ayah['text'], ayah['number'], settings)),
        ],
      ),
    );
  }

  Widget _buildGeneralPage(int pageNumber, SettingsProvider settings) {
    final ayahs = _getPageAyahs(pageNumber);
    
    return SingleChildScrollView(
      child: Column(
        children: [
          // البسملة للسور الجديدة (إلا التوبة)
          if (_isNewSurahStart(pageNumber) && !_isTawbahPage(pageNumber))
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: const Color(0xFF8B4513).withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: settings.fontSize + 2,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF8B4513),
                ),
              ),
            ),
          
          // آيات الصفحة
          ...ayahs.map((ayah) => _buildAyahContainer(ayah['text'], ayah['number'], settings)),
        ],
      ),
    );
  }

  // دالة للحصول على آيات الصفحة
  List<Map<String, dynamic>> _getPageAyahs(int pageNumber) {
    // آيات حقيقية من القرآن الكريم حسب الصفحة
    switch (pageNumber) {
      case 11:
        return [
          {'text': 'إِنَّ الَّذِينَ كَفَرُوا سَوَاءٌ عَلَيْهِمْ أَأَنذَرْتَهُمْ أَمْ لَمْ تُنذِرْهُمْ لَا يُؤْمِنُونَ', 'number': 6},
          {'text': 'خَتَمَ اللَّهُ عَلَىٰ قُلُوبِهِمْ وَعَلَىٰ سَمْعِهِمْ ۖ وَعَلَىٰ أَبْصَارِهِمْ غِشَاوَةٌ ۖ وَلَهُمْ عَذَابٌ عَظِيمٌ', 'number': 7},
          {'text': 'وَمِنَ النَّاسِ مَن يَقُولُ آمَنَّا بِاللَّهِ وَبِالْيَوْمِ الْآخِرِ وَمَا هُم بِمُؤْمِنِينَ', 'number': 8},
        ];
      case 12:
        return [
          {'text': 'يُخَادِعُونَ اللَّهَ وَالَّذِينَ آمَنُوا وَمَا يَخْدَعُونَ إِلَّا أَنفُسَهُمْ وَمَا يَشْعُرُونَ', 'number': 9},
          {'text': 'فِي قُلُوبِهِم مَّرَضٌ فَزَادَهُمُ اللَّهُ مَرَضًا ۖ وَلَهُمْ عَذَابٌ أَلِيمٌ بِمَا كَانُوا يَكْذِبُونَ', 'number': 10},
          {'text': 'وَإِذَا قِيلَ لَهُمْ لَا تُفْسِدُوا فِي الْأَرْضِ قَالُوا إِنَّمَا نَحْنُ مُصْلِحُونَ', 'number': 11},
        ];
      case 13:
        return [
          {'text': 'أَلَا إِنَّهُمْ هُمُ الْمُفْسِدُونَ وَلَٰكِن لَّا يَشْعُرُونَ', 'number': 12},
          {'text': 'وَإِذَا قِيلَ لَهُمْ آمِنُوا كَمَا آمَنَ النَّاسُ قَالُوا أَنُؤْمِنُ كَمَا آمَنَ السُّفَهَاءُ ۗ أَلَا إِنَّهُمْ هُمُ السُّفَهَاءُ وَلَٰكِن لَّا يَعْلَمُونَ', 'number': 13},
          {'text': 'وَإِذَا لَقُوا الَّذِينَ آمَنُوا قَالُوا آمَنَّا وَإِذَا خَلَوْا إِلَىٰ شَيَاطِينِهِمْ قَالُوا إِنَّا مَعَكُمْ إِنَّمَا نَحْنُ مُسْتَهْزِئُونَ', 'number': 14},
        ];
      case 50: // بداية سورة آل عمران
        return [
          {'text': 'الم', 'number': 1},
          {'text': 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ', 'number': 2},
          {'text': 'نَزَّلَ عَلَيْكَ الْكِتَابَ بِالْحَقِّ مُصَدِّقًا لِّمَا بَيْنَ يَدَيْهِ وَأَنزَلَ التَّوْرَاةَ وَالْإِنجِيلَ', 'number': 3},
        ];
      case 77: // بداية سورة النساء
        return [
          {'text': 'يَا أَيُّهَا النَّاسُ اتَّقُوا رَبَّكُمُ الَّذِي خَلَقَكُم مِّن نَّفْسٍ وَاحِدَةٍ وَخَلَقَ مِنْهَا زَوْجَهَا وَبَثَّ مِنْهُمَا رِجَالًا كَثِيرًا وَنِسَاءً ۚ وَاتَّقُوا اللَّهَ الَّذِي تَسَاءَلُونَ بِهِ وَالْأَرْحَامَ ۚ إِنَّ اللَّهَ كَانَ عَلَيْكُمْ رَقِيبًا', 'number': 1},
          {'text': 'وَآتُوا الْيَتَامَىٰ أَمْوَالَهُمْ ۖ وَلَا تَتَبَدَّلُوا الْخَبِيثَ بِالطَّيِّبِ ۖ وَلَا تَأْكُلُوا أَمْوَالَهُمْ إِلَىٰ أَمْوَالِكُمْ ۚ إِنَّهُ كَانَ حُوبًا كَبِيرًا', 'number': 2},
        ];
      case 106: // بداية سورة المائدة
        return [
          {'text': 'يَا أَيُّهَا الَّذِينَ آمَنُوا أَوْفُوا بِالْعُقُودِ ۚ أُحِلَّتْ لَكُم بَهِيمَةُ الْأَنْعَامِ إِلَّا مَا يُتْلَىٰ عَلَيْكُمْ غَيْرَ مُحِلِّي الصَّيْدِ وَأَنتُمْ حُرُمٌ ۗ إِنَّ اللَّهَ يَحْكُمُ مَا يُرِيدُ', 'number': 1},
          {'text': 'يَا أَيُّهَا الَّذِينَ آمَنُوا لَا تُحِلُّوا شَعَائِرَ اللَّهِ وَلَا الشَّهْرَ الْحَرَامَ وَلَا الْهَدْيَ وَلَا الْقَلَائِدَ وَلَا آمِّينَ الْبَيْتَ الْحَرَامَ يَبْتَغُونَ فَضْلًا مِّن رَّبِّهِمْ وَرِضْوَانًا', 'number': 2},
        ];
      default:
        // آيات عامة للصفحات الأخرى
        return [
          {'text': 'وَاتَّقُوا اللَّهَ وَاعْلَمُوا أَنَّ اللَّهَ بِكُلِّ شَيْءٍ عَلِيمٌ', 'number': 1},
          {'text': 'وَمَا أَرْسَلْنَاكَ إِلَّا رَحْمَةً لِّلْعَالَمِينَ', 'number': 2},
          {'text': 'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ', 'number': 3},
        ];
    }
  }

  // دالة للتحقق من بداية سورة جديدة
  bool _isNewSurahStart(int pageNumber) {
    final newSurahPages = [50, 77, 106, 128, 151, 177, 187, 208, 221, 235, 249, 255, 262, 267, 282, 293, 305, 312, 322, 332, 342, 350, 359, 367, 377, 385, 396, 404, 411, 415, 418, 428, 434, 440, 446, 453, 458, 467, 477, 483, 489, 496, 499, 502, 507, 511, 515, 518, 520, 523, 526, 528, 531, 534, 537, 542, 545, 549, 551, 553, 554, 556, 558, 560, 562, 564, 566, 568, 570, 572, 574, 577, 578, 580, 582, 583, 585, 586, 587, 589, 590, 591, 592, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604];
    return newSurahPages.contains(pageNumber);
  }

  // دالة للتحقق من صفحات سورة التوبة (بدون بسملة)
  bool _isTawbahPage(int pageNumber) {
    return pageNumber >= 187 && pageNumber <= 207; // صفحات سورة التوبة تقريباً
  }

  Widget _buildAyahContainer(String text, int ayahNumber, SettingsProvider settings) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            text,
            textAlign: TextAlign.justify,
            style: TextStyle(
              fontSize: settings.fontSize,
              height: 2.2,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF8B4513),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$ayahNumber',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getBaqarahAyahs(int pageNumber) {
    // آيات سورة البقرة حسب الصفحة
    switch (pageNumber) {
      case 2:
        return [
          {'text': 'الم', 'number': 1},
          {'text': 'ذَٰلِكَ الْكِتَابُ لَا رَيْبَ ۛ فِيهِ ۛ هُدًى لِّلْمُتَّقِينَ', 'number': 2},
          {'text': 'الَّذِينَ يُؤْمِنُونَ بِالْغَيْبِ وَيُقِيمُونَ الصَّلَاةَ وَمِمَّا رَزَقْنَاهُمْ يُنفِقُونَ', 'number': 3},
        ];
      case 3:
        return [
          {'text': 'وَالَّذِينَ يُؤْمِنُونَ بِمَا أُنزِلَ إِلَيْكَ وَمَا أُنزِلَ مِن قَبْلِكَ وَبِالْآخِرَةِ هُمْ يُوقِنُونَ', 'number': 4},
          {'text': 'أُولَٰئِكَ عَلَىٰ هُدًى مِّن رَّبِّهِمْ ۖ وَأُولَٰئِكَ هُمُ الْمُفْلِحُونَ', 'number': 5},
        ];
      default:
        return [
          {'text': 'آيات من سورة البقرة - صفحة $pageNumber', 'number': pageNumber - 1},
        ];
    }
  }

  String _getSurahName(int pageNumber) {
    if (pageNumber == 1) return 'سورة الفاتحة';
    if (pageNumber <= 49) return 'سورة البقرة';
    if (pageNumber <= 76) return 'سورة آل عمران';
    if (pageNumber <= 106) return 'سورة النساء';
    if (pageNumber <= 127) return 'سورة المائدة';
    return 'القرآن الكريم';
  }

  int _getJuzNumber(int pageNumber) {
    return ((pageNumber - 1) ~/ 20) + 1;
  }

  int _getHizbNumber(int pageNumber) {
    return ((pageNumber - 1) ~/ 10) + 1;
  }

  String _getAyahRange(int pageNumber) {
    return '1-10'; // مثال
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showPageSelector() {
    showDialog(
      context: context,
      builder: (context) {
        int selectedPage = _currentPage;
        return AlertDialog(
          title: const Text('الانتقال إلى صفحة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('اختر رقم الصفحة (1 - $_totalPages)'),
              const SizedBox(height: 16),
              TextField(
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'رقم الصفحة',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  selectedPage = int.tryParse(value) ?? _currentPage;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (selectedPage >= 1 && selectedPage <= _totalPages) {
                  _goToPage(selectedPage);
                  Navigator.pop(context);
                }
              },
              child: const Text('انتقال'),
            ),
          ],
        );
      },
    );
  }

  void _goToPage(int pageNumber) {
    _pageController.animateToPage(
      pageNumber - 1,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  void _showBookmarks() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة العلامات المرجعية قريباً'),
        backgroundColor: Color(0xFF8B4513),
      ),
    );
  }

  void _showSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Consumer<SettingsProvider>(
          builder: (context, settings, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'إعدادات القراءة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF8B4513),
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('حجم الخط'),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.remove),
                          onPressed: () => settings.setFontSize(settings.fontSize - 2),
                        ),
                        Text('${settings.fontSize.toInt()}'),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: () => settings.setFontSize(settings.fontSize + 2),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                    style: TextStyle(
                      fontSize: settings.fontSize,
                      height: 2.0,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}