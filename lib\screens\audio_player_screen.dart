import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../providers/settings_provider.dart';
import '../services/audio_service.dart';

class AudioPlayerScreen extends StatefulWidget {
  const AudioPlayerScreen({super.key});

  @override
  State<AudioPlayerScreen> createState() => _AudioPlayerScreenState();
}

class _AudioPlayerScreenState extends State<AudioPlayerScreen>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AudioProvider>(
        builder: (context, audioProvider, child) {
          // تحديث الرسوم المتحركة حسب حالة التشغيل
          if (audioProvider.isPlaying) {
            _rotationController.repeat();
          } else {
            _rotationController.stop();
          }

          return Container(
            decoration: _buildGradientBackground(),
            child: SafeArea(
              child: Column(
                children: [
                  // شريط التنقل العلوي
                  _buildTopBar(context),
                  
                  // المحتوى الرئيسي
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          const SizedBox(height: 20),
                          
                          // صورة السورة الدوارة
                          _buildRotatingCover(audioProvider),
                          
                          const SizedBox(height: 40),
                          
                          // معلومات السورة والقارئ
                          _buildSurahInfo(audioProvider),
                          
                          const SizedBox(height: 30),
                          
                          // شريط التقدم التفاعلي
                          _buildInteractiveProgressBar(audioProvider),
                          
                          const SizedBox(height: 40),
                          
                          // أزرار التحكم الرئيسية
                          _buildMainControls(audioProvider),
                          
                          const SizedBox(height: 30),
                          
                          // أزرار إضافية
                          _buildAdditionalControls(context, audioProvider),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  BoxDecoration _buildGradientBackground() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF1E3C72),
          Color(0xFF2A5298),
          Color(0xFF4CAF50),
        ],
        stops: [0.0, 0.6, 1.0],
      ),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // زر الرجوع
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(
                Icons.arrow_back_ios_rounded,
                color: Colors.white,
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          
          const Spacer(),
          
          // عنوان الصفحة
          const Text(
            'مشغل القرآن الكريم',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const Spacer(),
          
          // زر القائمة
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(
                Icons.more_vert_rounded,
                color: Colors.white,
              ),
              onPressed: () => _showOptionsMenu(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRotatingCover(AudioProvider audioProvider) {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // الظل الخارجي
          Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 30,
                  offset: const Offset(0, 15),
                ),
              ],
            ),
          ),
          
          // الغطاء الدوار
          AnimatedBuilder(
            animation: _rotationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationController.value * 2 * 3.14159,
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: Container(
                    width: 260,
                    height: 260,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFFFFD700),
                          Color(0xFFFFA500),
                          Color(0xFFFF8C00),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 3,
                      ),
                    ),
                    child: const Icon(
                      Icons.menu_book_rounded,
                      size: 80,
                      color: Colors.white,
                    ),
                  ),
                ),
              );
            },
          ),
          
          // النقطة المركزية
          Container(
            width: 20,
            height: 20,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSurahInfo(AudioProvider audioProvider) {
    return Column(
      children: [
        // اسم السورة
        Text(
          audioProvider.currentSurah?.name ?? 'غير محدد',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 8),
        
        // اسم القارئ
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.person_rounded,
                color: Colors.white,
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                audioProvider.currentReciter?.arabicName ?? 'غير محدد',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInteractiveProgressBar(AudioProvider audioProvider) {
    return Column(
      children: [
        // أوقات التشغيل
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(audioProvider.currentPosition),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                _formatDuration(audioProvider.totalDuration),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 8),
        
        // شريط التقدم
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: audioProvider.progress,
              backgroundColor: Colors.transparent,
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMainControls(AudioProvider audioProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // زر السابق
        _buildMainControlButton(
          icon: Icons.skip_previous_rounded,
          onPressed: audioProvider.canGoPrevious
            ? () => audioProvider.playPreviousSurah()
            : null,
          isEnabled: audioProvider.canGoPrevious,
          size: 60,
        ),
        
        // زر التشغيل/الإيقاف الرئيسي
        GestureDetector(
          onTapDown: (_) => _scaleController.forward(),
          onTapUp: (_) => _scaleController.reverse(),
          onTapCancel: () => _scaleController.reverse(),
          onTap: () {
            if (audioProvider.isPlaying) {
              audioProvider.pause();
            } else {
              audioProvider.resume();
            }
          },
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Icon(
                audioProvider.isPlaying 
                  ? Icons.pause_rounded 
                  : Icons.play_arrow_rounded,
                color: const Color(0xFF4CAF50),
                size: 40,
              ),
            ),
          ),
        ),
        
        // زر التالي
        _buildMainControlButton(
          icon: Icons.skip_next_rounded,
          onPressed: audioProvider.canGoNext
            ? () => audioProvider.playNextSurah()
            : null,
          isEnabled: audioProvider.canGoNext,
          size: 60,
        ),
      ],
    );
  }

  Widget _buildMainControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isEnabled,
    required double size,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: isEnabled ? 0.2 : 0.1),
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withValues(alpha: isEnabled ? 0.3 : 0.1),
          width: 2,
        ),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: Colors.white.withValues(alpha: isEnabled ? 1.0 : 0.5),
        ),
        onPressed: onPressed,
        iconSize: size * 0.4,
      ),
    );
  }

  Widget _buildAdditionalControls(BuildContext context, AudioProvider audioProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // زر الإيقاف
        _buildAdditionalControlButton(
          icon: Icons.stop_rounded,
          label: 'إيقاف',
          onPressed: () => audioProvider.stop(),
        ),
        
        // زر اختيار القارئ
        _buildAdditionalControlButton(
          icon: Icons.person_rounded,
          label: 'القارئ',
          onPressed: () => _showReciterSelector(context),
        ),
        
        // زر قائمة السور
        _buildAdditionalControlButton(
          icon: Icons.list_rounded,
          label: 'السور',
          onPressed: () => _showSurahList(context),
        ),
        
        // زر المشاركة
        _buildAdditionalControlButton(
          icon: Icons.share_rounded,
          label: 'مشاركة',
          onPressed: () => _shareCurrentSurah(audioProvider),
        ),
      ],
    );
  }

  Widget _buildAdditionalControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: Icon(
              icon,
              color: Colors.white,
            ),
            onPressed: onPressed,
            iconSize: 24,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  void _showOptionsMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.settings_rounded),
              title: const Text('الإعدادات'),
              onTap: () {
                Navigator.pop(context);
                // فتح صفحة الإعدادات
              },
            ),
            ListTile(
              leading: const Icon(Icons.download_rounded),
              title: const Text('تحميل السورة'),
              onTap: () {
                Navigator.pop(context);
                // تحميل السورة
              },
            ),
            ListTile(
              leading: const Icon(Icons.timer_rounded),
              title: const Text('مؤقت الإيقاف'),
              onTap: () {
                Navigator.pop(context);
                // ضبط مؤقت الإيقاف
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showReciterSelector(BuildContext context) {
    final reciters = AudioService.getDefaultReciters();
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'اختر القارئ',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: reciters.length,
                itemBuilder: (context, index) {
                  final reciter = reciters[index];
                  return Consumer<SettingsProvider>(
                    builder: (context, settings, child) {
                      final isSelected = settings.selectedReciterId == reciter.id;
                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isSelected 
                            ? const Color(0xFF4CAF50) 
                            : Colors.grey.shade200,
                          child: Icon(
                            Icons.person_rounded,
                            color: isSelected ? Colors.white : Colors.grey,
                          ),
                        ),
                        title: Text(
                          reciter.arabicName,
                          style: TextStyle(
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                        subtitle: Text(reciter.style),
                        trailing: isSelected 
                          ? const Icon(Icons.check_rounded, color: Color(0xFF4CAF50))
                          : null,
                        onTap: () {
                          settings.setSelectedReciter(reciter.id);
                          Navigator.pop(context);
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSurahList(BuildContext context) {
    // عرض قائمة السور
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('قائمة السور قريباً')),
    );
  }

  void _shareCurrentSurah(AudioProvider audioProvider) {
    // مشاركة السورة الحالية
    final surahName = audioProvider.currentSurah?.name ?? 'غير محدد';
    final reciterName = audioProvider.currentReciter?.arabicName ?? 'غير محدد';
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('مشاركة: $surahName - $reciterName')),
    );
  }
}