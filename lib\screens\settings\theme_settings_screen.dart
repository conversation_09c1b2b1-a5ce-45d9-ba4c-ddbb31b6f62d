import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/settings_provider.dart';
import '../../theme/app_themes.dart';
import '../../widgets/animated_card.dart';

class ThemeSettingsScreen extends StatefulWidget {
  const ThemeSettingsScreen({super.key});

  @override
  State<ThemeSettingsScreen> createState() => _ThemeSettingsScreenState();
}

class _ThemeSettingsScreenState extends State<ThemeSettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('تخصيص المظهر'),
            elevation: 0,
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
          ),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    Theme.of(context).colorScheme.surface,
                  ],
                ),
              ),
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // قسم الثيمات
                  _buildSectionTitle('اختيار الثيم', Icons.palette),
                  const SizedBox(height: 16),
                  _buildThemeSelector(settings),

                  const SizedBox(height: 32),

                  // قسم حجم الخط
                  _buildSectionTitle('حجم الخط', Icons.text_fields),
                  const SizedBox(height: 16),
                  _buildFontSizeSelector(settings),

                  const SizedBox(height: 32),

                  // قسم الإعدادات الإضافية
                  _buildSectionTitle('إعدادات إضافية', Icons.tune),
                  const SizedBox(height: 16),
                  _buildAdditionalSettings(settings),

                  const SizedBox(height: 32),

                  // زر إعادة التعيين
                  _buildResetButton(settings),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildThemeSelector(SettingsProvider settings) {
    final themes = AppThemes.getAvailableThemes();

    return AnimatedCard(
      child: Column(
        children: [
          // الوضع الليلي/النهاري
          SwitchListTile(
            title: const Text('الوضع الليلي'),
            subtitle: const Text('تفعيل الوضع الليلي المريح للعينين'),
            value: settings.isDarkMode,
            onChanged: (value) {
              settings.setDarkMode(value);
              _showSnackBar('تم ${value ? 'تفعيل' : 'إلغاء'} الوضع الليلي');
            },
            secondary: Icon(
              settings.isDarkMode ? Icons.dark_mode : Icons.light_mode,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),

          if (!settings.isDarkMode) ...[
            const Divider(),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'اختر لون الثيم المفضل:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),

            // شبكة الثيمات
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 3,
              ),
              itemCount: themes.length - 1, // استبعاد الوضع الليلي
              itemBuilder: (context, index) {
                final theme = themes[index];
                final isSelected = settings.themeColor == theme['key'];

                return GestureDetector(
                  onTap: () {
                    settings.setThemeColor(theme['key']);
                    _showSnackBar('تم تطبيق ثيم ${theme['name']}');
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    decoration: BoxDecoration(
                      color: theme['primaryColor'],
                      borderRadius: BorderRadius.circular(12),
                      border: isSelected
                          ? Border.all(color: Colors.white, width: 3)
                          : null,
                      boxShadow: [
                        BoxShadow(
                          color: theme['primaryColor'].withValues(alpha: 0.3),
                          blurRadius: isSelected ? 12 : 6,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                theme['icon'],
                                color: Colors.white,
                                size: 20,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                theme['name'],
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          const Positioned(
                            top: 8,
                            left: 8, // تغيير من right إلى left للاتجاه RTL
                            child: Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFontSizeSelector(SettingsProvider settings) {
    return AnimatedCard(
      child: Column(
        children: [
          ListTile(
            leading: Icon(
              Icons.text_fields,
              color: Theme.of(context).colorScheme.primary,
            ),
            title: const Text('حجم الخط'),
            subtitle: Text('الحجم الحالي: ${settings.getFontSizeCategory()}'),
            trailing: Text(
              '${settings.fontSize.toInt()}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              children: [
                Slider(
                  value: settings.fontSize,
                  min: 12,
                  max: 28,
                  divisions: 8,
                  label: settings.fontSize.toInt().toString(),
                  onChanged: (value) {
                    settings.setFontSize(value);
                  },
                ),

                // أزرار سريعة لأحجام الخط
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildFontSizeButton('صغير', 14, settings),
                    _buildFontSizeButton('متوسط', 18, settings),
                    _buildFontSizeButton('كبير', 22, settings),
                    _buildFontSizeButton('كبير جداً', 26, settings),
                  ],
                ),
              ],
            ),
          ),

          // معاينة النص
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
              style: TextStyle(
                fontSize: settings.fontSize,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFontSizeButton(String label, double size, SettingsProvider settings) {
    final isSelected = settings.fontSize == size;

    return GestureDetector(
      onTap: () => settings.setFontSize(size),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected
                ? Colors.white
                : Theme.of(context).colorScheme.primary,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildAdditionalSettings(SettingsProvider settings) {
    return AnimatedCard(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('الرسوم المتحركة'),
            subtitle: const Text('تفعيل الحركات والانتقالات السلسة'),
            value: settings.enableAnimations,
            onChanged: (value) {
              settings.setEnableAnimations(value);
              _showSnackBar('تم ${value ? 'تفعيل' : 'إلغاء'} الرسوم المتحركة');
            },
            secondary: Icon(
              Icons.animation,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),

          const Divider(),

          SwitchListTile(
            title: const Text('وضع إمكانية الوصول'),
            subtitle: const Text('واجهة مبسطة وخط أكبر لكبار السن'),
            value: settings.accessibilityMode,
            onChanged: (value) {
              settings.setAccessibilityMode(value);
              _showSnackBar('تم ${value ? 'تفعيل' : 'إلغاء'} وضع إمكانية الوصول');
            },
            secondary: Icon(
              Icons.accessibility,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResetButton(SettingsProvider settings) {
    return AnimatedCard(
      child: ListTile(
        leading: Icon(
          Icons.restore,
          color: Colors.orange[700],
        ),
        title: const Text('إعادة تعيين الإعدادات'),
        subtitle: const Text('استعادة الإعدادات الافتراضية'),
        trailing: const Icon(Icons.arrow_back_ios), // تغيير الاتجاه للـ RTL
        onTap: () => _showResetDialog(settings),
      ),
    );
  }

  void _showResetDialog(SettingsProvider settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text(
          'هل أنت متأكد من رغبتك في إعادة تعيين جميع إعدادات المظهر إلى القيم الافتراضية؟'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              settings.resetToDefaults();
              Navigator.pop(context);
              _showSnackBar('تم إعادة تعيين الإعدادات بنجاح');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[700],
            ),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }
}
