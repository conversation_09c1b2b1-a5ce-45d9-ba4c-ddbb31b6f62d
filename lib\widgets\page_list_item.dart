import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';
import '../screens/quran/juz_reading_screen.dart'; // Assuming page reading uses the same screen for now

class PageListItem extends StatelessWidget {
  final int pageNumber;

  const PageListItem({
    super.key,
    required this.pageNumber,
  });

  @override
  Widget build(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '$pageNumber',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        title: Text(
          'الصفحة $pageNumber',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        trailing: quranProvider.isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.arrow_forward_ios),
        onTap: () async {
          final quranProvider = Provider.of<QuranProvider>(context, listen: false);
          // Load page verses
          final verses = await quranProvider.getPageVerses(pageNumber);
          if (verses.isNotEmpty && context.mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => JuzReadingScreen(
                  juzNumber: pageNumber, // Using pageNumber as identifier
                  verses: verses,
                ),
              ),
            );
          } else if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في تحميل الصفحة'),
              ),
            );
          }
        },
      ),
    );
  }
}